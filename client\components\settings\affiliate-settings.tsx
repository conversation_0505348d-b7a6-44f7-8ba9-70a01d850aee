"use client"

import { useState } from "react"
import { Copy, Share2 } from "lucide-react"

export default function AffiliateSettings() {
  const [copied, setCopied] = useState(false)
  const affiliateCode = "FRIEND25"

  const copyToClipboard = () => {
    navigator.clipboard.writeText(affiliateCode)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  return (
    <div className="space-y-6">
      <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
        <h3 className="mb-2 text-lg font-medium">Your Affiliate Code</h3>
        <p className="mb-4 text-sm text-gray-500">
          Share your personal code with friends. They get 1 month free, and so do you for each person who subscribes!
        </p>

        <div className="relative mb-4">
          <div className="flex items-center overflow-hidden rounded-lg border border-gray-300 bg-gray-50">
            <div className="flex-1 px-3 py-2 font-mono text-lg font-semibold">{affiliateCode}</div>
            <button
              onClick={copyToClipboard}
              className="flex items-center gap-1 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200 rounded-lg"
            >
              {copied ? "Copied!" : "Copy"}
              <Copy className="h-4 w-4" />
            </button>
          </div>
        </div>

        <div className="mb-6 rounded-lg bg-green-50 p-4 text-sm text-green-600">
          <p className="font-medium">How it works:</p>
          <ul className="ml-5 mt-2 list-disc space-y-1">
            <li>Share your code with friends and family</li>
            <li>They get 1 month free when they subscribe</li>
            <li>You get 1 month free for each person who subscribes</li>
            <li>There&apos;s no limit to how many free months you can earn!</li>
          </ul>
        </div>

        <button className="flex w-full items-center justify-center gap-2 rounded-lg bg-green-500 px-4 py-2 text-center text-sm font-medium text-white hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
          <Share2 className="h-4 w-4" />
          Share Your Code
        </button>
      </div>

      <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
        <h3 className="mb-4 text-lg font-medium">Referral Stats</h3>

        <div className="grid grid-cols-2 gap-4">
          <div className="rounded-lg bg-gray-50 p-3 text-center">
            <p className="text-sm text-gray-500">Total Referrals</p>
            <p className="text-2xl font-bold">3</p>
          </div>
          <div className="rounded-lg bg-gray-50 p-3 text-center">
            <p className="text-sm text-gray-500">Free Months Earned</p>
            <p className="text-2xl font-bold">3</p>
          </div>
        </div>
      </div>
    </div>
  )
}
