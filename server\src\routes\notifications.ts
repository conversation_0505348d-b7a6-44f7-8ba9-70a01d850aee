import express from "express";
const router = express.Router();

import { Request, Response } from "express";
import { tryCatch } from "../middleware";
import { Account } from "../database/schemas";
import { AuthenticateTokenOAuth } from "../middleware/authentication";
import { getUserNotificationToken, sendCustomNotification, sendDefaultNotification } from "../controllers/notifications";




router.route("/save-token").post(AuthenticateTokenOAuth, tryCatch(async (req: Request, res: Response): Promise<any> => {
    const { token } = req.body;
    if (!token) {
        return res
            .status(400)
            .json({ success: false, message: "Token is required" });
    }

    const account = await Account.findById(req.user?._id);
    if (!account) {
        return res
            .status(404)
            .json({ success: false, message: "Account not found" });
    }

    account.notifications.expo_push_token = token;
    await account.save();

    res.status(200).json({
        success: true,
        message: "Notification token retrieved",
    });
}));





   

export default router
