import React, { useEffect, useRef } from 'react';
import { View, StyleSheet } from 'react-native';
import Animated, {
  useAnimatedStyle,
  useDerivedValue,
  useSharedValue,
  interpolate,
} from 'react-native-reanimated';
import { useDrawerProgress, useDrawerStatus } from '@react-navigation/drawer';
import { BlurView } from 'expo-blur';
import { triggerImpactSoftHaptic } from '@/utils/Haptics';

type Props = {
  children: React.ReactNode;
};

export const ScreenWrapper = ({ children }: Props) => {
  const progress = useDrawerProgress();
  const isDrawerOpen = useDrawerStatus() === 'open';

  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: interpolate(progress.value, [0, 1], [0, 1]),
    };
  });

  useEffect(() => {
    triggerImpactSoftHaptic()
  }, [isDrawerOpen]);



  return (
    <View style={{ flex: 1 }}>
      {children}
      <Animated.View style={[StyleSheet.absoluteFill, animatedStyle]}>
        <BlurView intensity={25} tint="light" style={StyleSheet.absoluteFill} />
      </Animated.View>
    </View>
  );
};
