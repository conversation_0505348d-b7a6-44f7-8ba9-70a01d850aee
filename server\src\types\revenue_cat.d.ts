export interface Entitlement {
  expires_date: string | null;
  product_identifier: string;
  is_active: boolean;
}

export interface Subscription {
  expires_date: string | null;
  period_type: string;
}

export interface CustomerInfo {
  entitlements: { [key: string]: Entitlement };
  subscriptions: { [key: string]: Subscription };
  original_app_user_id: string;
  first_seen: string;
  non_subscriptions: { [key: string]: any };
  other_purchases: { [key: string]: any };
}

export interface RevenueCatResponse {
  request_date: string;
  request_date_ms: number;
  subscriber: CustomerInfo;
}