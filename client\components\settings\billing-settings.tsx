"use client"

import { useState } from "react"
import { CreditCard, Check } from "lucide-react"

export default function BillingSettings() {
  const [selectedPlan, setSelectedPlan] = useState("monthly")

  const plans = [
    {
      id: "monthly",
      name: "Monthly",
      price: "$2.99",
      period: "per month",
      description: "Billed monthly",
    },
    {
      id: "quarterly",
      name: "3 Months",
      price: "$7.99",
      period: "per quarter",
      description: "Save 10%",
    },
    {
      id: "yearly",
      name: "Yearly",
      price: "$19.99",
      period: "per year",
      description: "Save 17%",
    },
  ]

  return (
    <div className="space-y-6">
      <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
        <h3 className="mb-4 text-lg font-medium">Payment Method</h3>

        <div className="mb-4 flex items-center gap-3 rounded-lg border border-gray-200 p-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-100">
            <CreditCard className="h-5 w-5 text-gray-600" />
          </div>
          <div>
            <p className="font-medium">•••• •••• •••• 4242</p>
            <p className="text-sm text-gray-500">Expires 12/25</p>
          </div>
        </div>

        <button className="rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">
          Update Payment Method
        </button>
      </div>

      <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
        <h3 className="mb-4 text-lg font-medium">Subscription Plans</h3>

        <div className="space-y-3">
          {plans.map((plan) => (
            <div
              key={plan.id}
              className={`relative cursor-pointer rounded-lg border p-4 transition-all ${
                selectedPlan === plan.id ? "border-green-500 bg-green-50" : "border-gray-200 hover:border-gray-300"
              }`}
              onClick={() => setSelectedPlan(plan.id)}
            >
              <div className="flex items-start justify-between">
                <div>
                  <h4 className="font-medium">{plan.name}</h4>
                  <div className="mt-1 flex items-baseline">
                    <span className="text-xl font-semibold">{plan.price}</span>
                    <span className="ml-1 text-sm text-gray-500">{plan.period}</span>
                  </div>
                  <p className="mt-1 text-sm text-gray-500">{plan.description}</p>
                </div>
                {selectedPlan === plan.id && (
                  <div className="rounded-full bg-green-500 p-1 text-white">
                    <Check className="h-4 w-4" />
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        <button className="mt-4 w-full rounded-lg bg-green-500 px-4 py-2 text-center text-sm font-medium text-white hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
          Update Subscription
        </button>
      </div>
    </div>
  )
}
