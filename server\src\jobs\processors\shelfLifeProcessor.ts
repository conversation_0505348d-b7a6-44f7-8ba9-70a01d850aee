import { Job } from 'bull';
import mongoose from 'mongoose';
import { FridgeItem, FoodShelfLife } from '../../database/schemas';
import { ShelfLifeService } from '../../services';
import { notificationQueue } from '../queue';

const shelfLifeService = new ShelfLifeService();

export interface ShelfLifeLookupJobData {
  foodName: string;
  fridgeItemId?: string;
  userId?: string;
  category?: string;
  priority?: 'high' | 'medium' | 'low';
}

export interface BatchShelfLifeJobData {
  unknownFoods: Array<{
    foodName: string;
    fridgeItemIds: string[];
    userIds: string[];
  }>;
}

/**
 * Process individual shelf life lookup
 */
export const processShelfLifeLookup = async (job: Job<ShelfLifeLookupJobData>) => {
  const { foodName, fridgeItemId, userId, category } = job.data;
  
  try {
    await job.progress(10);
    console.log(`Processing shelf life lookup for: ${foodName}`);

    // Get shelf life information from AI service
    const shelfLifeResult = await shelfLifeService.getShelfLife(foodName, category);
    
    if (!shelfLifeResult.success || !shelfLifeResult.data) {
      throw new Error(`Failed to get shelf life for ${foodName}: ${shelfLifeResult.error}`);
    }

    await job.progress(50);

    const shelfLifeData = shelfLifeResult.data;
    
    // Store the shelf life data in database for future use
    const storeResult = await shelfLifeService.storeShelfLifeData(shelfLifeData);
    
    if (!storeResult.success) {
      console.warn(`Failed to store shelf life data for ${foodName}:`, storeResult.error);
    }

    await job.progress(70);

    // If we have a specific fridge item, update its expiry date
    if (fridgeItemId && mongoose.Types.ObjectId.isValid(fridgeItemId)) {
      const fridgeItem = await FridgeItem.findById(fridgeItemId);
      
      if (fridgeItem) {
        // Calculate new expiry date
        const today = new Date();
        const newExpiryDate = new Date(today);
        newExpiryDate.setDate(today.getDate() + shelfLifeData.averageShelfLifeDays);
        
        // Update the fridge item
        fridgeItem.expiryDate = newExpiryDate;
        await fridgeItem.save();
        
        console.log(`Updated expiry date for fridge item ${fridgeItemId}: ${newExpiryDate.toISOString()}`);
        
        // Send notification to user if available
        if (userId) {
          await notificationQueue.add('send-push-notification', {
            userId,
            type: 'shelf_life_updated',
            title: 'Expiry Date Updated',
            body: `We've updated the expiry date for ${foodName} based on our shelf life database.`,
            data: {
              fridgeItemId,
              foodName,
              newExpiryDate: newExpiryDate.toISOString(),
              shelfLifeDays: shelfLifeData.averageShelfLifeDays,
              confidence: shelfLifeData.confidence
            }
          }, {
            delay: 2000, // Send notification after 2 seconds
            attempts: 2
          });
        }
      }
    }

    await job.progress(100);

    return {
      success: true,
      foodName,
      shelfLifeData,
      fridgeItemUpdated: !!fridgeItemId,
      notificationSent: !!userId
    };

  } catch (error: any) {
    console.error(`Shelf life lookup failed for ${foodName}:`, error.message);
    
    // Send error notification if we have a user
    if (userId) {
      await notificationQueue.add('send-push-notification', {
        userId,
        type: 'shelf_life_lookup_failed',
        title: 'Shelf Life Lookup Failed',
        body: `We couldn't determine the shelf life for ${foodName}. Using default expiry date.`,
        data: {
          foodName,
          error: error.message,
          fridgeItemId
        }
      }, {
        attempts: 1
      });
    }

    throw error;
  }
};

/**
 * Process batch shelf life lookup for unknown items
 */
export const processBatchShelfLifeLookup = async (job: Job<BatchShelfLifeJobData>) => {
  const { unknownFoods } = job.data;
  
  try {
    console.log(`Processing batch shelf life lookup for ${unknownFoods.length} unknown foods`);
    
    const results: Array<{
      foodName: string;
      success: boolean;
      shelfLifeData?: any;
      error?: string;
    }> = [];

    let processed = 0;
    
    // Process each unknown food
    for (const unknownFood of unknownFoods) {
      try {
        const { foodName, fridgeItemIds, userIds } = unknownFood;
        
        // Get shelf life information
        const shelfLifeResult = await shelfLifeService.getShelfLife(foodName);
        
        if (shelfLifeResult.success && shelfLifeResult.data) {
          const shelfLifeData = shelfLifeResult.data;
          
          // Store in database
          await shelfLifeService.storeShelfLifeData(shelfLifeData);
          
          // Update all related fridge items
          if (fridgeItemIds.length > 0) {
            const today = new Date();
            const newExpiryDate = new Date(today);
            newExpiryDate.setDate(today.getDate() + shelfLifeData.averageShelfLifeDays);
            
            await FridgeItem.updateMany(
              { _id: { $in: fridgeItemIds.map(id => new mongoose.Types.ObjectId(id)) } },
              { expiryDate: newExpiryDate }
            );
            
            console.log(`Updated ${fridgeItemIds.length} fridge items for ${foodName}`);
          }
          
          // Send notifications to affected users
          for (const userId of userIds) {
            await notificationQueue.add('send-push-notification', {
              userId,
              type: 'batch_shelf_life_updated',
              title: 'Shelf Life Information Updated',
              body: `We've updated shelf life information for ${foodName} and similar items.`,
              data: {
                foodName,
                shelfLifeDays: shelfLifeData.averageShelfLifeDays,
                confidence: shelfLifeData.confidence,
                affectedItems: fridgeItemIds.length
              }
            }, {
              delay: Math.random() * 10000, // Random delay up to 10 seconds
              attempts: 2
            });
          }
          
          results.push({
            foodName,
            success: true,
            shelfLifeData
          });
          
        } else {
          results.push({
            foodName,
            success: false,
            error: shelfLifeResult.error || 'Unknown error'
          });
        }
        
      } catch (error: any) {
        results.push({
          foodName: unknownFood.foodName,
          success: false,
          error: error.message
        });
      }
      
      processed++;
      await job.progress(Math.round((processed / unknownFoods.length) * 100));
      
      // Add small delay between items to avoid overwhelming the AI service
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    const successCount = results.filter(r => r.success).length;
    console.log(`Batch shelf life lookup completed: ${successCount}/${unknownFoods.length} successful`);

    return {
      success: true,
      totalProcessed: unknownFoods.length,
      successCount,
      failureCount: unknownFoods.length - successCount,
      results
    };

  } catch (error: any) {
    console.error('Batch shelf life lookup failed:', error.message);
    throw error;
  }
};

/**
 * Find and queue unknown foods for batch processing
 */
export const queueUnknownFoodsProcessing = async () => {
  try {
    console.log('Finding unknown foods for batch processing...');

    // Find fridge items with foods not in our shelf life database
    const unknownFoodsAggregation = await FridgeItem.aggregate([
      {
        $lookup: {
          from: 'foodshelflifes',
          let: { itemName: { $toLower: { $trim: { input: '$name' } } } },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ['$normalizedFoodName', '$$itemName']
                }
              }
            }
          ],
          as: 'shelfLifeData'
        }
      },
      {
        $match: {
          shelfLifeData: { $size: 0 } // No shelf life data found
        }
      },
      {
        $group: {
          _id: { $toLower: { $trim: { input: '$name' } } },
          foodName: { $first: '$name' },
          fridgeItemIds: { $push: { $toString: '$_id' } },
          userIds: { $push: { $toString: '$userId' } },
          count: { $sum: 1 }
        }
      },
      {
        $match: {
          count: { $gte: 2 } // Only process foods that appear in multiple items
        }
      },
      {
        $sort: { count: -1 }
      },
      {
        $limit: 20 // Process top 20 unknown foods
      }
    ]);

    if (unknownFoodsAggregation.length === 0) {
      console.log('No unknown foods found for batch processing');
      return { success: true, queuedJobs: 0 };
    }

    const unknownFoods = unknownFoodsAggregation.map(item => ({
      foodName: item.foodName,
      fridgeItemIds: item.fridgeItemIds,
      userIds: [...new Set(item.userIds)] // Remove duplicates
    }));

    // Queue batch processing job
    const { shelfLifeQueue } = await import('../queue');
    
    const job = await shelfLifeQueue.add('batch-shelf-life-lookup', {
      unknownFoods
    }, {
      attempts: 2,
      backoff: {
        type: 'exponential',
        delay: 5000
      },
      removeOnComplete: 5,
      removeOnFail: 10
    });

    console.log(`Queued batch shelf life processing job ${job.id} for ${unknownFoods.length} unknown foods`);

    return {
      success: true,
      queuedJobs: 1,
      unknownFoodsCount: unknownFoods.length,
      jobId: job.id
    };

  } catch (error: any) {
    console.error('Failed to queue unknown foods processing:', error.message);
    throw error;
  }
};
