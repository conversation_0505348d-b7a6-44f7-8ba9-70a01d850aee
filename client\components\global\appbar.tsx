'use client'

import { Leaf } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'

const links = [
	{ label: 'Logbook', href: '/logbook' },
	{ label: 'Settings', href: '/settings' },
]

const Appbar = () => {

	return (
		<div className='fixed top-0 left-0 z-20 w-full bg-[#ffffffa0] pt-safe' style={{ backdropFilter: 'blur(10px)' }}>
			<header className=' bg-white px-safe  dark:bg-[#ffffff00]'>
			{/* dark:border-zinc-100 border-b */}
				<div className='mx-auto flex h-20 max-w-screen-md items-center justify-between px-6'>
					<Link href='/' className='flex flex-row items-center space-x-2'>
					<Leaf className="h-6 w-6 text-green-500" />
						<h1 className='font-medium text-[#00000080]'>Leftover Chef</h1>
					</Link>

					<nav className='flex items-center space-x-6'>
						<div className='hidden sm:block'>
							<div className='flex items-center space-x-6'>
								{links.map(({ label, href }) => (
									<Link
										key={label}
										href={href}
										className={`text-sm text-[#00000080]`}
										/* ${
											router.pathname === href
												? 'text-indigo-500 dark:text-indigo-400'
												: 'text-zinc-600 hover:text-zinc-900 dark:text-zinc-400 dark:hover:text-zinc-50'
										} */
									>
										{label}
									</Link>
								))}
							</div>
						</div>

						<img
							src='/images/pfp-fallback.webp'
							title='Gluten Free'
							className='h-9 w-9 rounded-full bg-zinc-200 bg-cover bg-center shadow-inner dark:bg-zinc-800'
							
						/>
					</nav>
				</div>
			</header>
		</div>
	)
}

export default Appbar
