

import FoodLoader from '@/components/atoms/loaders/Food'
import ScreenLoader from '@/components/atoms/loaders/Screen'
import Page from '@/components/templates/Page'
import { AuthContext } from '@/context/AuthContext'
import { useRouter } from 'expo-router'
import React, { useContext, useEffect } from 'react'




const Index = () => {
  const router = useRouter()
  const {handleInitialAuthentication} = useContext(AuthContext)
  
  const handleCheckAuthentication = async() => {
    const call = await handleInitialAuthentication()
  }

  useEffect(()=>{
    handleCheckAuthentication()
  },[])


  return (
    <Page alignItems='center' justifyContent='center'>
      <ScreenLoader/>
      <FoodLoader/>
    </Page>
  )
}



export default Index

