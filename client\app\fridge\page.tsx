'use client'

import AddIngredientForm from "@/components/cards/add-ingredient-form";
import FridgeStatistics from "@/components/cards/fridge-statistics";
import FridgeStatsPanel from "@/components/cards/fridge-stats-panel";
import IngredientsList from "@/components/cards/ingredients-list";
import MobileModal from "@/components/cards/mobile-modal";
import Page from "@/components/global/page";
import Section from "@/components/global/section";



import { useEffect, useState } from "react";

interface Ingredient {
  id: string
  name: string
}

/* const initialIngredients = [
  { id: "1", name: "Flour" },
  { id: "2", name: "<PERSON>" },
  { id: "3", name: "Egg<PERSON>" },
  { id: "4", name: "<PERSON>" },
  { id: "5", name: "Butter" }
] */

export default function Home() {
  const [ingredients, setIngredients] = useState<Ingredient[]>([])

  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isStatsOpen, setIsStatsOpen] = useState(false)

  useEffect(() => {
    console.log("Ingredients: ", ingredients)
  }, [ingredients])


  return (
    <Page
      noPadding
    >
      <Section>
        <div style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          height: "100%",
          color: 'black',
        }}>

          {
            ingredients.length > 0 &&
            <FridgeStatsPanel
              clickRefill={() => setIsModalOpen(true)}
            />
          }

          <IngredientsList
            initialIngredients={ingredients}
            clickRefill={() => setIsModalOpen(true)}
            clickStats={() => setIsStatsOpen(true)}
          />
        </div>

        {/* Fridge Fill Modal */}
        <MobileModal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} height="65%">
          <AddIngredientForm
            onAdd={(ingredient) => {
              setIngredients((prev) => [...prev, { id: ingredient, name: ingredient }])
              setIsModalOpen(false)
            }}
            onClose={() => setIsModalOpen(false)}
          />

        </MobileModal>

        {/* Fridge Stats Modal */}
        <MobileModal isOpen={isStatsOpen} onClose={() => setIsStatsOpen(false)} height="77%">
          <FridgeStatistics />
        </MobileModal>

      </Section>
    </Page>
  );
}
