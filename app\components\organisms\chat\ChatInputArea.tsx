import React, { useContext, useEffect, useState } from 'react'

import { Ionicons } from '@expo/vector-icons'
import { TextInput } from 'react-native-gesture-handler'
import { Keyboard, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import { router } from 'expo-router'
import { useChat } from '@/context/ChatContext'
import { useBottomSheet } from '@/context/BottomSheetContext'
import i18n from '@/i18n/i18n'


interface ChatInputAreaProps {

}

const ChatInputArea = ({

}: ChatInputAreaProps) => {
  const { handleSendMessage } = useChat()
  const { handleToggleBottomSheet } = useBottomSheet()

  const [inputText, setInputText] = useState('')
  const [keyboardVisible, setKeyboardVisible] = useState(false);


  useEffect(() => {
    // Add keyboard event listeners
    const keyboardDidShowListener = Keyboard.addListener('keyboardWillShow', () => {
      setKeyboardVisible(true); // Keyboard is visible
      console.log('Keyboard is up');
    });

    const keyboardDidHideListener = Keyboard.addListener('keyboardWillHide', () => {
      setKeyboardVisible(false); // Keyboard is hidden
      console.log('Keyboard is down');
    });

    // Clean up listeners on unmount
    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);


  const renderBottomSheet = () => {

    return (
      <View>

      </View>
    )
  }

  return (

    < View style={[styles.inputContainer, {
      paddingBottom: keyboardVisible ? 10 : 44
    }]} >
      <View style={styles.inputWrapper}>
        <TextInput
          style={styles.textInput}
          placeholder={i18n.t('home.chatboxPlaceholder')}
          placeholderTextColor="#999"
          value={inputText}
          onChangeText={setInputText}
          multiline
          maxLength={500}
          
        />
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', width: '100%', }}>
          <TouchableOpacity
            style={[styles.sendButtonInactive, { height: 30, paddingHorizontal: 10, alignItems: 'center', justifyContent: 'center', borderRadius: 30, padding: 2 }]}
            onPress={() => { handleToggleBottomSheet(renderBottomSheet()) }}
          >
            <Text style={{color: "#999", fontWeight: 500}}>{i18n.t('home.selectIngredients')}</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[inputText.trim() ? styles.sendButtonActive : styles.sendButtonInactive, { height: 30, width: 30, alignItems: 'center', justifyContent: 'center', borderRadius: 30, padding: 2 }]}
            onPress={() => { handleSendMessage(inputText); setInputText('') }}
            disabled={!inputText.trim()}
          >
            <Ionicons name="arrow-up" size={20} color={inputText.trim() ? "#fff" : "#999"} />
          </TouchableOpacity>
        </View>
      </View>

    </View >
  )
}

export default ChatInputArea

const styles = StyleSheet.create({
  inputContainer: {

    paddingHorizontal: 16,
    width: '100%'
  },
  inputWrapper: {
    flexDirection: "column",
    alignItems: "center",
    backgroundColor: "#00000010",
    borderWidth: 1.7,
    borderColor: "#00000020",
    borderRadius: 20,
    paddingHorizontal: 8,
    paddingVertical: 12,
    
    width: '100%',
    height: 90,
    
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: "#333",
    width: '100%',
    
  },
  sendButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: "center",
    justifyContent: "center",
    marginLeft: 8,
  },
  sendButtonActive: {
    
    backgroundColor: 'rgba(34,197,94,0.7)'
  },
  sendButtonInactive: {
    backgroundColor: "transparent",
    borderWidth: 1,
    borderColor: '#00000030',
    borderRadius: 40
  },
  quickActions: {
    flexDirection: "row",
    gap: 8,
  },
  quickAction: {
    backgroundColor: "#f8f9fa",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: "#e9ecef",
    width: "47%"
  },
  quickActionText: {
    fontSize: 12,
    color: "#666",
  },
  bottomNav: {
    flexDirection: "row",
    backgroundColor: "#fff",
    borderTopWidth: 1,
    borderTopColor: "#e9ecef",
    paddingVertical: 12,
    paddingHorizontal: 20,
  },
  navItem: {
    flex: 1,
    alignItems: "center",
    gap: 4,
  },
  navText: {
    fontSize: 12,
    color: "#999",
  },
  activeNavText: {
    color: "rgba(34,197,94,0.7)",
    fontWeight: "500",
  },
})