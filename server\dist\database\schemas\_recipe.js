"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
const mongoose_1 = tslib_1.__importDefault(require("mongoose"));
const recipeIngredientSchema = new mongoose_1.default.Schema({
    name: {
        type: String,
        required: [true, 'Ingredient name is required'],
        trim: true
    },
    quantity: {
        type: Number,
        required: [true, 'Ingredient quantity is required'],
        min: [0, 'Ingredient quantity must be positive']
    },
    unit: {
        type: String,
        required: [true, 'Ingredient unit is required'],
        trim: true
    }
}, { _id: false });
const recipeSchema = new mongoose_1.default.Schema({
    title: {
        type: String,
        required: [true, 'Recipe title is required'],
        trim: true,
        maxlength: [200, 'Recipe title cannot exceed 200 characters'],
        minlength: [1, 'Recipe title cannot be empty']
    },
    ingredients: {
        type: [recipeIngredientSchema],
        required: [true, 'Recipe ingredients are required'],
        validate: {
            validator: function (ingredients) {
                return ingredients && ingredients.length > 0;
            },
            message: 'Recipe must have at least one ingredient'
        }
    },
    steps: {
        type: [String],
        required: [true, 'Recipe steps are required'],
        validate: {
            validator: function (steps) {
                return steps && steps.length > 0 && steps.every(step => step.trim().length > 0);
            },
            message: 'Recipe must have at least one non-empty step'
        }
    },
    userId: {
        type: mongoose_1.default.Schema.Types.ObjectId,
        ref: 'Account',
        required: false, // Nullable for system recipes
        index: true
    },
    source: {
        type: String,
        required: [true, 'Recipe source is required'],
        enum: {
            values: ['AI', 'DATABASE', 'USER'],
            message: 'Source must be one of: AI, DATABASE, USER'
        },
        default: 'USER'
    },
    isLiked: {
        type: Boolean,
        default: false
    },
    ingredientHash: {
        type: String,
        required: [true, 'Ingredient hash is required'],
        index: true
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Compound indexes for efficient queries
recipeSchema.index({ userId: 1, createdAt: -1 });
recipeSchema.index({ userId: 1, isLiked: 1 });
recipeSchema.index({ userId: 1, source: 1 });
recipeSchema.index({ ingredientHash: 1, userId: 1 });
// Virtual for ingredient count
recipeSchema.virtual('ingredientCount').get(function () {
    return this.ingredients ? this.ingredients.length : 0;
});
// Virtual for step count
recipeSchema.virtual('stepCount').get(function () {
    return this.steps ? this.steps.length : 0;
});
// Static method to generate ingredient hash
recipeSchema.statics.generateIngredientHash = function (ingredients) {
    // Sort ingredients alphabetically and create hash
    const sortedIngredients = ingredients.map(ing => ing.toLowerCase().trim()).sort();
    return require('crypto').createHash('md5').update(sortedIngredients.join('|')).digest('hex');
};
exports.default = mongoose_1.default.model('Recipe', recipeSchema);
