import React, { useContext, useEffect, useState } from 'react'
import { Image, Pressable, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import { signInWithApple } from '@/utils/AppleAuth'
import { GoogleSignin, isSuccessResponse } from '@react-native-google-signin/google-signin'

import Page from '@/components/templates/Page'
import ScreenLoader from '@/components/atoms/loaders/Screen'
import ButtonIcon from '@/components/atoms/buttons/ButtonIcon'
import AppleIcon from '@/components/atoms/icons/Apple'
import GoogleIcon from '@/components/atoms/icons/Google'
import Toast from 'react-native-toast-message'

import { router } from 'expo-router'
import { AuthContext } from '@/context/AuthContext'
import { appleLogin, googleLogin, setToken } from '@/services/api'
import ScreenSpinnerLoader from '@/components/atoms/loaders/ScreenSpinner'

const Login = () => {
  const { sessionAuthentication } = useContext(AuthContext)
  const [loading, setLoading] = useState(false)

  const handleAppleLogin = async () => {
    const { identityToken } = await signInWithApple()

    if (!identityToken) {
      Toast.show({
        type: 'error',
        text1: 'Apple Sign In Failed',
        text2: 'Please try again later.',
      })
      return
    }
    setLoading(true)
    const response = await appleLogin(identityToken)
    if (!response.success) {
      Toast.show({
        type: 'error',
        text1: 'Apple Login Failed',
        text2: response.error || 'Please try again later.',
      })
      setLoading(false)
      return
    }

    //Set Token in Secure Store
    setToken(response.token)

    //Go to Home
    await sessionAuthentication()
    setLoading(false)
    router.replace('/(home)')
  }
  const handleGoogleLogin = async () => {
    try {
      await GoogleSignin.hasPlayServices()
      const response = await GoogleSignin.signIn()

      if (isSuccessResponse(response)) {
        const { user } = response.data
        const { givenName, familyName, email, photo, id } = user

        setLoading(true)
        const call = await googleLogin(id, email, givenName ?? '', familyName ?? '', photo ?? '')

        if (!call.success) {
          setLoading(false)
          Toast.show({
            type: 'error',
            text1: 'Google Login Failed',
            text2: 'Please try again later.',
          })
          return
        }

        //Set Token in Secure Store
        setToken(call.token)

        //Go to Home
        await sessionAuthentication()
        setLoading(false)
        router.replace('/(home)')

      } else {
        Toast.show({
          type: 'error',
          text1: 'Google Login Failed',
          text2: 'Please try again later.',
        })
        setLoading(false)
      }
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Google Login Failed',
        text2: 'Please try again later.',
      })
      setLoading(false)
    }
  }


  useEffect(() => {
    GoogleSignin.configure({
      iosClientId: "684414254538-iea4399buqu70spud45kkq457i5mpckv.apps.googleusercontent.com",
      webClientId: "684414254538-5spcgehljv9153jsamtlunalcj3nco43.apps.googleusercontent.com",
      profileImageSize: 150
    })
  }, [])


  return (
    <Page noPaddingTop alignItems='center' justifyContent='space-between' >
      <ScreenLoader />
      {
        loading &&
        <ScreenSpinnerLoader />
      }
      {/* Top Part */}
      <View style={{ alignItems: 'center', justifyContent: 'center', flexDirection: 'column', gap: 15, marginTop: 180 }}>

        {/* Logo App */}
        <View style={{ borderWidth: 1, borderColor: '#00000010', padding: 25, borderRadius: 30, backgroundColor: 'white' }}>
          <Image
            source={require('@/assets/pictures/logo.png')}
            style={{ width: 70, height: 70, resizeMode: 'contain' }}
          />
        </View>

        {/* Title */}
        <Image
          source={require('@/assets/pictures/logotipo-green.png')}
          style={{ height: 30, resizeMode: 'contain', marginTop: 10 }}
        />

        {/* Subtitle */}
        <Text style={{ fontSize: 18 }}>Cook & Save</Text>
      </View>

      {/* Bottom Part */}
      <View style={{ alignItems: 'center', justifyContent: 'flex-end', flexDirection: 'column', gap: 20, marginBottom: 30, width: '100%' }}>

        {/* Login Form */}


        <ButtonIcon
          icon={<AppleIcon />}
          text='Sign in with Apple'
          onPress={() => {
            handleAppleLogin()
          }}
          style={{ width: '70%', backgroundColor: 'white' }}
          styleText={{ fontSize: 15, color: 'black', fontWeight: '600' }}
        />
        <ButtonIcon
          icon={<GoogleIcon />}
          text='Sign in with Google'
          onPress={() => { handleGoogleLogin() }}
          style={{ width: '70%', backgroundColor: 'white' }}
          styleText={{ fontSize: 15, color: 'black', fontWeight: '600' }}
        />

        <View style={{ marginTop: 120, flexDirection: 'row', justifyContent: 'center' }}>
          <Text style={{ fontSize: 12, color: '#00000080' }}>
            By signing in you agree to the
            <Text
              style={{ color: '#1000ffa9' }}
              onPress={() => router.push('/settings/terms')}
            >
              {' '}Terms of Service
            </Text>
            {' '}and
            <Text
              style={{ color: '#1000ffa9' }}
              onPress={() => router.push('/settings/policy')}
            >
              {' '}Privacy Policy
            </Text>
          </Text>
        </View>
      </View>
    </Page>
  )
}

export default Login

const styles = StyleSheet.create({})