import { ObjectId } from 'mongoose';

// Base interfaces
export interface FridgeItemBase {
  name: string;
  quantity: number;
  unit: 'pieces' | 'kg' | 'g' | 'l' | 'ml' | 'cups' | 'tbsp' | 'tsp';
  expiryDate: Date;
  isFavorite: boolean;
}

export interface FridgeItem extends FridgeItemBase {
  id: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
  daysUntilExpiry: number;
  expiryStatus: 'expired' | 'expires_today' | 'expires_soon' | 'expires_this_week' | 'fresh';
}

// DTOs for API requests
export interface CreateFridgeItemDto {
  name: string;
  quantity: number;
  unit: 'pieces' | 'kg' | 'g' | 'l' | 'ml' | 'cups' | 'tbsp' | 'tsp';
  expiryDate: string; // ISO date string
  isFavorite?: boolean;
}

export interface UpdateFridgeItemDto {
  name?: string;
  quantity?: number;
  unit?: 'pieces' | 'kg' | 'g' | 'l' | 'ml' | 'cups' | 'tbsp' | 'tsp';
  expiryDate?: string; // ISO date string
  isFavorite?: boolean;
}

// DTOs for API responses
export interface FridgeItemResponseDto {
  id: string;
  name: string;
  quantity: number;
  unit: FridgeItemUnit;
  expiryDate: string; // ISO date string
  isFavorite: boolean;
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  daysUntilExpiry: number;
  expiryStatus: ExpiryStatus;
}

export interface FridgeItemsListResponseDto {
  success: boolean;
  data: FridgeItemResponseDto[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface FridgeItemResponseSingleDto {
  success: boolean;
  data: FridgeItemResponseDto;
  message?: string;
}

// Query parameters for listing fridge items
export interface FridgeItemsQueryParams {
  page?: number;
  limit?: number;
  sortBy?: 'expiryDate' | 'createdAt' | 'name';
  sortOrder?: 'asc' | 'desc';
  expiryStatus?: 'expired' | 'expires_today' | 'expires_soon' | 'expires_this_week' | 'fresh';
  isFavorite?: boolean;
  search?: string;
}

// Recommendation types
export interface RecommendationItem extends FridgeItemResponseDto {
  priority: 'high' | 'medium' | 'low';
  reason: string;
}

export interface RecommendationsResponseDto {
  success: boolean;
  data: RecommendationItem[];
  message?: string;
}

// Validation schemas (for use with validation middleware)
export interface FridgeItemValidationRules {
  name: {
    required: true;
    type: 'string';
    minLength: 1;
    maxLength: 100;
    trim: true;
  };
  quantity: {
    required: true;
    type: 'number';
    min: 0.01;
    decimalPlaces: 2;
  };
  unit: {
    required: true;
    type: 'string';
    enum: ['pieces', 'kg', 'g', 'l', 'ml', 'cups', 'tbsp', 'tsp'];
  };
  expiryDate: {
    required: true;
    type: 'date';
    minDate: 'tomorrow';
  };
  isFavorite: {
    required: false;
    type: 'boolean';
    default: false;
  };
}

// Error types
export interface FridgeItemError {
  field: string;
  message: string;
  code: string;
}

export interface FridgeItemValidationError {
  success: false;
  statusCode: 400;
  message: string;
  errors: FridgeItemError[];
  timestamp: string;
}
