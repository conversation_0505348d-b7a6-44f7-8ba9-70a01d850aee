
import React, { useEffect, useRef } from 'react'

import animationData from "../../public/animations/splashscreen.json"; // adjust path accordingly
import Lottie from 'lottie-react';


interface LoaderProps {
    isLoading: boolean
    fullscreen?: boolean

    animationSize?: {
        width: number
        height: number
    }
}

function Loader({ isLoading, fullscreen, animationSize }: LoaderProps) {
    const lottieRef = useRef<any>(null);

    useEffect(() => {
        if (lottieRef.current) {
            lottieRef.current.setSpeed(3); // 2x speed
        }
    }, []);


    if (!isLoading) return null

    if (!fullscreen) return (
        <div
            
        >
            <Lottie
                animationData={animationData}
                loop={true}
                lottieRef={lottieRef}
                style={{
                    width: animationSize?.width || 100,
                    height: animationSize?.height || 100,
                }}
            />
        </div>
    )


    if (fullscreen) return (
        <div
            style={{
                position: fullscreen ? 'fixed' : 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: 'white',
                zIndex: 9999,
            }}
        >
            <Lottie
                animationData={animationData}
                loop={true}
                lottieRef={lottieRef}
                className='w-1/2 h-1/2'
            />
        </div>
    )
}

export default Loader