"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validatePagination = exports.validateMongoId = exports.validateRecipesQuery = exports.validateGenerateRecipe = exports.validateFridgeItemsQuery = exports.validateUpdateFridgeItem = exports.validateCreateFridgeItem = exports.validate = exports.mongoIdSchema = exports.recipesQuerySchema = exports.generateRecipeSchema = exports.fridgeItemsQuerySchema = exports.updateFridgeItemSchema = exports.createFridgeItemSchema = void 0;
const zod_1 = require("zod");
// Validation schemas
exports.createFridgeItemSchema = zod_1.z.object({
    body: zod_1.z.object({
        name: zod_1.z.string()
            .trim()
            .min(1, 'Item name is required')
            .max(100, 'Item name cannot exceed 100 characters'),
        quantity: zod_1.z.number()
            .positive('Quantity must be positive')
            .refine((val) => Number(val.toFixed(2)) === val, {
            message: 'Quantity can have at most 2 decimal places'
        }),
        unit: zod_1.z.enum(['pieces', 'kg', 'g', 'l', 'ml', 'cups', 'tbsp', 'tsp']),
        expiryDate: zod_1.z.string()
            .refine((date) => {
            const expiryDate = new Date(date);
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            tomorrow.setHours(0, 0, 0, 0);
            return expiryDate >= tomorrow;
        }, {
            message: 'Expiry date must be at least tomorrow'
        }),
        isFavorite: zod_1.z.boolean().optional().default(false)
    })
});
exports.updateFridgeItemSchema = zod_1.z.object({
    body: zod_1.z.object({
        name: zod_1.z.string()
            .trim()
            .min(1, 'Item name cannot be empty')
            .max(100, 'Item name cannot exceed 100 characters')
            .optional(),
        quantity: zod_1.z.number()
            .positive('Quantity must be positive')
            .refine((val) => Number(val.toFixed(2)) === val, {
            message: 'Quantity can have at most 2 decimal places'
        })
            .optional(),
        unit: zod_1.z.enum(['pieces', 'kg', 'g', 'l', 'ml', 'cups', 'tbsp', 'tsp']).optional(),
        expiryDate: zod_1.z.string()
            .refine((date) => {
            const expiryDate = new Date(date);
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            tomorrow.setHours(0, 0, 0, 0);
            return expiryDate >= tomorrow;
        }, {
            message: 'Expiry date must be at least tomorrow'
        })
            .optional(),
        isFavorite: zod_1.z.boolean().optional()
    })
});
exports.fridgeItemsQuerySchema = zod_1.z.object({
    query: zod_1.z.object({
        page: zod_1.z.string().regex(/^\d+$/).transform(Number).optional(),
        limit: zod_1.z.string().regex(/^\d+$/).transform(Number).optional(),
        sortBy: zod_1.z.enum(['expiryDate', 'createdAt', 'name']).optional(),
        sortOrder: zod_1.z.enum(['asc', 'desc']).optional(),
        expiryStatus: zod_1.z.enum(['expired', 'expires_today', 'expires_soon', 'expires_this_week', 'fresh']).optional(),
        isFavorite: zod_1.z.string().transform((val) => val === 'true').optional(),
        search: zod_1.z.string().optional()
    })
});
exports.generateRecipeSchema = zod_1.z.object({
    body: zod_1.z.object({
        fridgeItemIds: zod_1.z.array(zod_1.z.string().min(1, 'Invalid fridge item ID'))
            .min(1, 'At least one fridge item ID is required')
            .max(20, 'Cannot generate recipe with more than 20 ingredients'),
        preferences: zod_1.z.object({
            cuisine: zod_1.z.string().optional(),
            difficulty: zod_1.z.enum(['easy', 'medium', 'hard']).optional(),
            cookingTime: zod_1.z.number().positive().max(480).optional(), // max 8 hours
            servings: zod_1.z.number().positive().max(20).optional()
        }).optional()
    })
});
exports.recipesQuerySchema = zod_1.z.object({
    query: zod_1.z.object({
        page: zod_1.z.string().regex(/^\d+$/).transform(Number).optional(),
        limit: zod_1.z.string().regex(/^\d+$/).transform(Number).optional(),
        sortBy: zod_1.z.enum(['createdAt', 'title', 'ingredientCount']).optional(),
        sortOrder: zod_1.z.enum(['asc', 'desc']).optional(),
        source: zod_1.z.enum(['AI', 'DATABASE', 'USER']).optional(),
        isLiked: zod_1.z.string().transform((val) => val === 'true').optional(),
        search: zod_1.z.string().optional(),
        dateFrom: zod_1.z.string().refine((date) => !isNaN(Date.parse(date)), {
            message: 'Invalid date format for dateFrom'
        }).optional(),
        dateTo: zod_1.z.string().refine((date) => !isNaN(Date.parse(date)), {
            message: 'Invalid date format for dateTo'
        }).optional()
    })
});
exports.mongoIdSchema = zod_1.z.object({
    params: zod_1.z.object({
        id: zod_1.z.string().regex(/^[0-9a-fA-F]{24}$/, 'Invalid MongoDB ObjectId format')
    })
});
// Validation middleware factory
const validate = (schema) => {
    return (req, res, next) => {
        try {
            const result = schema.parse({
                body: req.body,
                query: req.query,
                params: req.params
            });
            // Update request objects with validated data
            if (result.body)
                req.body = result.body;
            if (result.query)
                req.query = result.query;
            if (result.params)
                req.params = result.params;
            next();
        }
        catch (error) {
            if (error instanceof zod_1.z.ZodError) {
                const validationErrors = error.issues.map(issue => ({
                    field: issue.path.join('.'),
                    message: issue.message,
                    code: issue.code
                }));
                res.status(400).json({
                    success: false,
                    statusCode: 400,
                    message: 'Validation failed',
                    errors: validationErrors,
                    timestamp: new Date().toISOString()
                });
                return;
            }
            // Handle other validation errors
            res.status(500).json({
                success: false,
                statusCode: 500,
                message: 'Internal validation error',
                timestamp: new Date().toISOString()
            });
        }
    };
};
exports.validate = validate;
// Specific validation middleware
exports.validateCreateFridgeItem = (0, exports.validate)(exports.createFridgeItemSchema);
exports.validateUpdateFridgeItem = (0, exports.validate)(exports.updateFridgeItemSchema);
exports.validateFridgeItemsQuery = (0, exports.validate)(exports.fridgeItemsQuerySchema);
exports.validateGenerateRecipe = (0, exports.validate)(exports.generateRecipeSchema);
exports.validateRecipesQuery = (0, exports.validate)(exports.recipesQuerySchema);
exports.validateMongoId = (0, exports.validate)(exports.mongoIdSchema);
// Custom validation for pagination limits
const validatePagination = (req, res, next) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    if (page < 1) {
        res.status(400).json({
            success: false,
            statusCode: 400,
            message: 'Page number must be greater than 0',
            timestamp: new Date().toISOString()
        });
        return;
    }
    if (limit < 1 || limit > 100) {
        res.status(400).json({
            success: false,
            statusCode: 400,
            message: 'Limit must be between 1 and 100',
            timestamp: new Date().toISOString()
        });
        return;
    }
    req.query.page = page.toString();
    req.query.limit = limit.toString();
    next();
};
exports.validatePagination = validatePagination;
