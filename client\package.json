{"name": "leftover-chef", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-slot": "^1.2.0", "cmdk": "^1.1.1", "framer-motion": "^12.8.0", "lottie-react": "^2.4.1", "lucide-react": "^0.503.0", "next": "15.2.4", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.4", "tailwindcss": "^4", "typescript": "^5"}}