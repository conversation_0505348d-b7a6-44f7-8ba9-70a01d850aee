import mongoose from 'mongoose';
import { Recipe, UserRecipeUsage, FridgeItem } from '../database/schemas';
import { CacheService } from './CacheService';
import {
  GenerateRecipeDto,
  CreateRecipeDto,
  UpdateRecipeDto,
  RecipesQueryParams,
  RecipeFilters,
  ServiceResponse,
  UserTier,
  AIRecipeRequest,
  AIRecipeResponse
} from '../types';

export class RecipeService {
  private cacheService: CacheService;

  constructor() {
    this.cacheService = new CacheService();
  }
  /**
   * Generate a recipe from selected fridge items
   */
  async generateRecipe(
    userId: string,
    fridgeItemIds: string[],
    userTier: UserTier = 'FREE'
  ): Promise<ServiceResponse<any>> {
    try {
      // Check daily generation limits
      const limitCheck = await this.checkDailyLimit(userId, userTier);
      if (!limitCheck.success) {
        return limitCheck;
      }

      // Get fridge items
      const fridgeItems = await FridgeItem.find({
        _id: { $in: fridgeItemIds.map(id => new mongoose.Types.ObjectId(id)) },
        userId: new mongoose.Types.ObjectId(userId)
      }).lean();

      if (fridgeItems.length === 0) {
        return {
          success: false,
          error: 'No valid fridge items found',
          statusCode: 400
        };
      }

      // Create ingredient hash for duplicate detection
      const ingredientNames = fridgeItems.map(item => item.name.toLowerCase().trim()).sort();
      const ingredientHash = require('crypto')
        .createHash('md5')
        .update(ingredientNames.join('|'))
        .digest('hex');

      // First check cache for existing recipe
      let cachedRecipe = await this.cacheService.getCachedRecipe(ingredientHash);

      if (cachedRecipe) {
        console.log(`Recipe found in cache for hash: ${ingredientHash}`);
        return {
          success: true,
          data: {
            recipe: cachedRecipe,
            isFromCache: true,
            usageInfo: await this.getUserUsageInfo(userId, userTier)
          }
        };
      }

      // Search for existing recipe in database
      let existingRecipe = await Recipe.findOne({
        ingredientHash,
        $or: [
          { userId: new mongoose.Types.ObjectId(userId) },
          { userId: null } // System recipes
        ]
      }).lean();

      if (existingRecipe) {
        const formattedRecipe = this.formatRecipeResponse(existingRecipe);

        // Cache the recipe for future use
        await this.cacheService.cacheRecipe(ingredientHash, formattedRecipe, 24 * 60 * 60); // 24 hours

        // Return existing recipe without incrementing usage
        return {
          success: true,
          data: {
            recipe: formattedRecipe,
            isFromCache: true,
            usageInfo: await this.getUserUsageInfo(userId, userTier)
          }
        };
      }

      // Generate new recipe using AI service
      const aiRequest: AIRecipeRequest = {
        ingredients: ingredientNames
      };

      const aiRecipe = await this.callAIService(aiRequest);
      if (!aiRecipe) {
        return {
          success: false,
          error: 'Failed to generate recipe from AI service',
          statusCode: 500
        };
      }

      // Create and save new recipe
      const newRecipe = new Recipe({
        title: aiRecipe.title,
        ingredients: aiRecipe.ingredients,
        steps: aiRecipe.steps,
        userId: new mongoose.Types.ObjectId(userId),
        source: 'AI',
        isLiked: false,
        ingredientHash
      });

      const savedRecipe = await newRecipe.save();
      const formattedRecipe = this.formatRecipeResponse(savedRecipe);

      // Cache the newly generated recipe
      await this.cacheService.cacheRecipe(ingredientHash, formattedRecipe, 24 * 60 * 60); // 24 hours

      // Increment user's daily usage counter
      await UserRecipeUsage.incrementUsage(
        new mongoose.Types.ObjectId(userId),
        userTier
      );

      return {
        success: true,
        data: {
          recipe: formattedRecipe,
          isFromCache: false,
          usageInfo: await this.getUserUsageInfo(userId, userTier)
        }
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        statusCode: 500
      };
    }
  }

  /**
   * Check if user has reached daily generation limit
   */
  private async checkDailyLimit(userId: string, userTier: UserTier): Promise<ServiceResponse<any>> {
    try {
      const usage = await UserRecipeUsage.getOrCreateTodayUsage(
        new mongoose.Types.ObjectId(userId),
        userTier
      );

      const dailyLimit = userTier === 'PRO' ? 15 : 5;

      if (usage.generatedCount >= dailyLimit) {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(0, 0, 0, 0);

        return {
          success: false,
          error: 'Daily recipe generation limit reached',
          statusCode: 429,
          data: {
            dailyUsed: usage.generatedCount,
            dailyLimit,
            resetTime: tomorrow.toISOString()
          }
        };
      }

      return { success: true };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        statusCode: 500
      };
    }
  }

  /**
   * Get user's current usage information
   */
  private async getUserUsageInfo(userId: string, userTier: UserTier) {
    const usage = await UserRecipeUsage.getOrCreateTodayUsage(
      new mongoose.Types.ObjectId(userId),
      userTier
    );

    const dailyLimit = userTier === 'PRO' ? 15 : 5;

    return {
      dailyUsed: usage.generatedCount,
      dailyLimit,
      remainingGenerations: Math.max(0, dailyLimit - usage.generatedCount)
    };
  }

  /**
   * Toggle like status of a recipe
   */
  async likeRecipe(userId: string, recipeId: string): Promise<ServiceResponse<any>> {
    try {
      const recipe = await Recipe.findOne({
        _id: new mongoose.Types.ObjectId(recipeId),
        $or: [
          { userId: new mongoose.Types.ObjectId(userId) },
          { userId: null } // System recipes can be liked by anyone
        ]
      });

      if (!recipe) {
        return {
          success: false,
          error: 'Recipe not found or access denied',
          statusCode: 404
        };
      }

      recipe.isLiked = !recipe.isLiked;
      const updatedRecipe = await recipe.save();

      return {
        success: true,
        data: this.formatRecipeResponse(updatedRecipe)
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        statusCode: 500
      };
    }
  }

  /**
   * Get user's recipes with filtering
   */
  async getUserRecipes(
    userId: string,
    queryParams: RecipesQueryParams = {}
  ): Promise<ServiceResponse<{ recipes: any[], pagination: any }>> {
    try {
      const {
        page = 1,
        limit = 20,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        source,
        isLiked,
        search,
        dateFrom,
        dateTo
      } = queryParams;

      // Build filter query
      const filter: any = {
        $or: [
          { userId: new mongoose.Types.ObjectId(userId) },
          { userId: null } // Include system recipes
        ]
      };

      if (source) {
        filter.source = source;
      }

      if (isLiked !== undefined) {
        filter.isLiked = isLiked;
      }

      if (search) {
        // Preserve existing $or conditions and extend with search conditions
        const searchConditions = [
          { title: { $regex: search, $options: 'i' } },
          { 'ingredients.name': { $regex: search, $options: 'i' } }
        ];

        if (filter.$or) {
          // If $or already exists, combine with AND logic:
          // (existing $or conditions) AND (search conditions)
          filter.$and = [
            { $or: filter.$or },
            { $or: searchConditions }
          ];
          delete filter.$or;
        } else {
          // If no existing $or, just set the search conditions
          filter.$or = searchConditions;
        }
      }

      if (dateFrom || dateTo) {
        filter.createdAt = {};
        if (dateFrom) filter.createdAt.$gte = new Date(dateFrom);
        if (dateTo) filter.createdAt.$lte = new Date(dateTo);
      }

      // Build sort object
      const sort: any = {};
      sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

      // Calculate pagination
      const skip = (page - 1) * limit;

      // Execute queries
      const [recipes, total] = await Promise.all([
        Recipe.find(filter)
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        Recipe.countDocuments(filter)
      ]);

      // Calculate pagination metadata
      const totalPages = Math.ceil(total / limit);
      const pagination = {
        page,
        limit,
        total,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      };

      return {
        success: true,
        data: {
          recipes: recipes.map(recipe => this.formatRecipeResponse(recipe)),
          pagination
        }
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        statusCode: 500
      };
    }
  }

  /**
   * Get daily limit information for user
   */
  async getDailyLimitInfo(userId: string, userTier: UserTier): Promise<ServiceResponse<any>> {
    try {
      const usage = await UserRecipeUsage.getOrCreateTodayUsage(
        new mongoose.Types.ObjectId(userId),
        userTier
      );

      const dailyLimit = userTier === 'PRO' ? 15 : 5;
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);

      return {
        success: true,
        data: {
          dailyUsed: usage.generatedCount,
          dailyLimit,
          remainingGenerations: Math.max(0, dailyLimit - usage.generatedCount),
          usagePercentage: Math.min(100, (usage.generatedCount / dailyLimit) * 100),
          canGenerateMore: usage.generatedCount < dailyLimit,
          userTier,
          resetTime: tomorrow.toISOString()
        }
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        statusCode: 500
      };
    }
  }

  /**
   * Call AI service to generate recipe (placeholder implementation)
   */
  private async callAIService(request: AIRecipeRequest): Promise<AIRecipeResponse | null> {
    try {
      // This is a placeholder implementation
      //TODO: implement the AI system and recipe generation service

      // For now, return a mock recipe
      const mockRecipe: AIRecipeResponse = {
        title: `Delicious ${request.ingredients.join(' and ')} Recipe`,
        ingredients: request.ingredients.map((ingredient, index) => ({
          name: ingredient,
          quantity: Math.floor(Math.random() * 3) + 1,
          unit: ['cups', 'pieces', 'tbsp'][Math.floor(Math.random() * 3)]
        })),
        steps: [
          `Prepare all ingredients: ${request.ingredients.join(', ')}.`,
          'Heat a pan over medium heat.',
          'Add the ingredients to the pan and cook for 10-15 minutes.',
          'Season with salt and pepper to taste.',
          'Serve hot and enjoy!'
        ],
        estimatedCookingTime: 30,
        servings: 4,
        difficulty: 'easy'
      };

      return mockRecipe;
    } catch (error) {
      console.error('AI service call failed:', error);
      return null;
    }
  }

  /**
   * Format recipe for API response
   */
  private formatRecipeResponse(recipe: any): any {
    return {
      id: recipe._id.toString(),
      title: recipe.title,
      ingredients: recipe.ingredients,
      steps: recipe.steps,
      userId: recipe.userId?.toString(),
      source: recipe.source,
      isLiked: recipe.isLiked,
      ingredientHash: recipe.ingredientHash,
      createdAt: recipe.createdAt.toISOString(),
      updatedAt: recipe.updatedAt.toISOString(),
      ingredientCount: recipe.ingredients?.length || 0,
      stepCount: recipe.steps?.length || 0
    };
  }
}
