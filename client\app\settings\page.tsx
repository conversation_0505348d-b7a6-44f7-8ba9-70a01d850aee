'use client'

import Page from "@/components/global/page";
import PageBack from "@/components/global/page-back";
import Section from "@/components/global/section";


import AccountSettings from "@/components/settings/account-settings";
import AffiliateSettings from "@/components/settings/affiliate-settings";
import BillingSettings from "@/components/settings/billing-settings";
import TermsSettings from "@/components/settings/terms-settings";
import { ChevronRight, CreditCard, FileText, Headphones, LogOut, Share2, Trash2, User } from "lucide-react";

import Image from "next/image";
import { useState } from "react";

/* interface Item {
  id: string
  label: string
} */

export default function Home() {
  const [activePage, setActivePage] = useState<string | null>(null)
  const menuItems = [
    {
      id: "account",
      label: "Account",
      icon: <User className="h-5 w-5" />,
      component: <AccountSettings />,
    },
    {
      id: "billing",
      label: "Billing",
      icon: <CreditCard className="h-5 w-5" />,
      component: <BillingSettings />,
    },
    {
      id: "affiliate",
      label: "Affiliate",
      icon: <Share2 className="h-5 w-5" />,
      component: <AffiliateSettings />,
    },
    {
      id: "terms",
      label: "Terms & Services",
      icon: <FileText className="h-5 w-5" />,
      component: <TermsSettings />,
    },
    {
      id: "support",
      label: "Support",
      icon: <Headphones className="h-5 w-5" />,
      component: <TermsSettings />,
      onClick: () => {
        handleGoToSupport()
      }
    },
  ]


  const handleGoToSupport = () => {
    const email = "<EMAIL>"; // change to your setup email
    const subject = encodeURIComponent("Support LeftoverChef");
    const body = encodeURIComponent("Hi there,\n\n ");

    const mailtoLink = `mailto:${email}`
    //- ?subject=${subject}&body=${body}`;

    window.location.href = mailtoLink;
  }


  if (activePage) {
    const selectedItem = menuItems.find((item) => item.id === activePage)

    return (
      <PageBack

        title="Settings"
        noPadding
        goBack={() => setActivePage(null)}
      >
        <Section>
          <div style={{
            height: "100%",
            width: "100%",
            color: 'black'
          }}>
            <div className="flex min-h-screen flex-col">
              <main className="flex-1">{selectedItem?.component}</main>
            </div>
          </div>
        </Section>
      </PageBack>
    )
  }



  return (
    <Page noPadding>
      <Section>
        <div style={{
          display: "flex",
          flexDirection: "column",
          height: "100%",
          color: 'black',
        }}>
          {/* Main content */}
          <main className="flex-1 p-0">





            {/* Settings menu */}
            <div className="mb-6 overflow-hidden rounded-lg border border-gray-200 bg-white">
              {menuItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => {
                    if (item.onClick) {
                      item.onClick()
                    } else {
                      setActivePage(item.id)
                    }
                  }}
                  className="flex w-full items-center justify-between border-b border-gray-200 px-4 py-2 text-left hover:bg-gray-50 last:border-b-0"
                >
                  <div className="flex items-center gap-3">
                    <div className="flex h-9 w-9 items-center justify-center rounded-full bg-gray-100 text-gray-600">
                      {item.icon}
                    </div>
                    <span>{item.label}</span>
                  </div>
                  <ChevronRight className="h-5 w-5 text-gray-400" />
                </button>
              ))}
            </div>

            {/* Images Leafs */}
            <div style={{ position: 'relative', height: '188px' }}>
              <Image
                src="/logo/leftover-chef_green.png"
                alt="Settings Banner"
                width={250}
                height={200}
                className="object-cover"
                style={{
                  position: 'absolute'
                }}
              />
              <Image
                src="/logo/leftover-chef_color.png"
                alt="Settings Banner"
                width={250}
                height={200}
                className="object-cover"
                style={{
                  position: 'absolute',
                  right: '0%'
                }}
              />
            </div>

            {/* Account actions */}
            <div className="space-y-2">
              <button className="flex w-full items-center gap-3 rounded-lg border border-gray-300 bg-white px-4 py-2 text-left hover:bg-gray-50">
                <LogOut className="h-5 w-5 text-gray-600" />
                <span>Logout</span>
              </button>
              <button className="flex w-full items-center gap-3 rounded-lg border border-red-300 bg-white px-4 py-2 text-left text-red-600 hover:bg-red-50">
                <Trash2 className="h-5 w-5" />
                <span>Delete Account</span>
              </button>
            </div>

            <div className="flex flex-col items-center mt-10">
              <Image
                src='/logo/hazel-logo-black_color.png'
                height={40}
                width={40}
                alt=""
              />
              <p className="text-[12px]">Powered by Hazel</p>
              <p className="text-[10px]">2025</p>
            </div>

          </main>

        </div>

      </Section>
    </Page>
  );
}
