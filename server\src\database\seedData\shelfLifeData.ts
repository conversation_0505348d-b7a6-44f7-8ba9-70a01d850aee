import { FoodShelfLife } from '../schemas';

export const commonFoodShelfLife = [
  // Fruits
  { foodName: 'Apple', averageShelfLifeDays: 14, category: 'fruits', confidence: 0.95, source: 'DATABASE' as const },
  { foodName: 'Banana', averageShelfLifeDays: 5, category: 'fruits', confidence: 0.9, source: 'DATABASE' as const },
  { foodName: 'Orange', averageShelfLifeDays: 10, category: 'fruits', confidence: 0.9, source: 'DATABASE' as const },
  { foodName: 'Grapes', averageShelfLifeDays: 7, category: 'fruits', confidence: 0.85, source: 'DATABASE' as const },
  { foodName: 'Strawberry', averageShelfLifeDays: 3, category: 'fruits', confidence: 0.9, source: 'DATABASE' as const },
  { foodName: 'Blueberry', averageShelfLifeDays: 5, category: 'fruits', confidence: 0.85, source: 'DATABASE' as const },
  { foodName: 'Lemon', averageShelfLifeDays: 21, category: 'fruits', confidence: 0.9, source: 'DATABASE' as const },
  { foodName: 'Lime', averageShelfLifeDays: 14, category: 'fruits', confidence: 0.9, source: 'DATABASE' as const },

  // Vegetables
  { foodName: 'Tomato', averageShelfLifeDays: 7, category: 'vegetables', confidence: 0.9, source: 'DATABASE' as const },
  { foodName: 'Lettuce', averageShelfLifeDays: 7, category: 'vegetables', confidence: 0.85, source: 'DATABASE' as const },
  { foodName: 'Carrot', averageShelfLifeDays: 21, category: 'vegetables', confidence: 0.9, source: 'DATABASE' as const },
  { foodName: 'Broccoli', averageShelfLifeDays: 5, category: 'vegetables', confidence: 0.85, source: 'DATABASE' as const },
  { foodName: 'Spinach', averageShelfLifeDays: 5, category: 'vegetables', confidence: 0.8, source: 'DATABASE' as const },
  { foodName: 'Cucumber', averageShelfLifeDays: 7, category: 'vegetables', confidence: 0.85, source: 'DATABASE' as const },
  { foodName: 'Bell Pepper', averageShelfLifeDays: 7, category: 'vegetables', confidence: 0.85, source: 'DATABASE' as const },
  { foodName: 'Onion', averageShelfLifeDays: 30, category: 'vegetables', confidence: 0.95, source: 'DATABASE' as const },
  { foodName: 'Garlic', averageShelfLifeDays: 90, category: 'vegetables', confidence: 0.9, source: 'DATABASE' as const },
  { foodName: 'Potato', averageShelfLifeDays: 30, category: 'vegetables', confidence: 0.9, source: 'DATABASE' as const },

  // Dairy
  { foodName: 'Milk', averageShelfLifeDays: 7, category: 'dairy', confidence: 0.95, source: 'DATABASE' as const },
  { foodName: 'Yogurt', averageShelfLifeDays: 14, category: 'dairy', confidence: 0.9, source: 'DATABASE' as const },
  { foodName: 'Cheese', averageShelfLifeDays: 21, category: 'dairy', confidence: 0.9, source: 'DATABASE' as const },
  { foodName: 'Butter', averageShelfLifeDays: 30, category: 'dairy', confidence: 0.9, source: 'DATABASE' as const },
  { foodName: 'Cream', averageShelfLifeDays: 7, category: 'dairy', confidence: 0.85, source: 'DATABASE' as const },
  { foodName: 'Sour Cream', averageShelfLifeDays: 14, category: 'dairy', confidence: 0.85, source: 'DATABASE' as const },

  // Meat & Poultry
  { foodName: 'Chicken Breast', averageShelfLifeDays: 3, category: 'poultry', confidence: 0.95, source: 'DATABASE' as const },
  { foodName: 'Ground Beef', averageShelfLifeDays: 2, category: 'meat', confidence: 0.95, source: 'DATABASE' as const },
  { foodName: 'Pork Chops', averageShelfLifeDays: 3, category: 'meat', confidence: 0.9, source: 'DATABASE' as const },
  { foodName: 'Salmon', averageShelfLifeDays: 2, category: 'seafood', confidence: 0.9, source: 'DATABASE' as const },
  { foodName: 'Shrimp', averageShelfLifeDays: 2, category: 'seafood', confidence: 0.85, source: 'DATABASE' as const },

  // Baked Goods
  { foodName: 'Bread', averageShelfLifeDays: 5, category: 'baked_goods', confidence: 0.9, source: 'DATABASE' as const },
  { foodName: 'Bagel', averageShelfLifeDays: 3, category: 'baked_goods', confidence: 0.85, source: 'DATABASE' as const },
  { foodName: 'Muffin', averageShelfLifeDays: 3, category: 'baked_goods', confidence: 0.8, source: 'DATABASE' as const },

  // Herbs & Spices
  { foodName: 'Basil', averageShelfLifeDays: 7, category: 'herbs', confidence: 0.8, source: 'DATABASE' as const },
  { foodName: 'Parsley', averageShelfLifeDays: 10, category: 'herbs', confidence: 0.8, source: 'DATABASE' as const },
  { foodName: 'Cilantro', averageShelfLifeDays: 7, category: 'herbs', confidence: 0.8, source: 'DATABASE' as const },
  { foodName: 'Rosemary', averageShelfLifeDays: 14, category: 'herbs', confidence: 0.85, source: 'DATABASE' as const },

  // Grains & Legumes
  { foodName: 'Rice', averageShelfLifeDays: 365, category: 'grains', confidence: 0.95, source: 'DATABASE' as const },
  { foodName: 'Pasta', averageShelfLifeDays: 365, category: 'grains', confidence: 0.95, source: 'DATABASE' as const },
  { foodName: 'Quinoa', averageShelfLifeDays: 365, category: 'grains', confidence: 0.9, source: 'DATABASE' as const },
  { foodName: 'Black Beans', averageShelfLifeDays: 365, category: 'legumes', confidence: 0.9, source: 'DATABASE' as const },
  { foodName: 'Lentils', averageShelfLifeDays: 365, category: 'legumes', confidence: 0.9, source: 'DATABASE' as const },

  // Condiments
  { foodName: 'Ketchup', averageShelfLifeDays: 180, category: 'condiments', confidence: 0.9, source: 'DATABASE' as const },
  { foodName: 'Mustard', averageShelfLifeDays: 365, category: 'condiments', confidence: 0.9, source: 'DATABASE' as const },
  { foodName: 'Mayonnaise', averageShelfLifeDays: 60, category: 'condiments', confidence: 0.85, source: 'DATABASE' as const },
  { foodName: 'Soy Sauce', averageShelfLifeDays: 365, category: 'condiments', confidence: 0.95, source: 'DATABASE' as const },

  // Beverages
  { foodName: 'Orange Juice', averageShelfLifeDays: 7, category: 'beverages', confidence: 0.9, source: 'DATABASE' as const },
  { foodName: 'Apple Juice', averageShelfLifeDays: 7, category: 'beverages', confidence: 0.9, source: 'DATABASE' as const },
  { foodName: 'Wine', averageShelfLifeDays: 1095, category: 'beverages', confidence: 0.8, source: 'DATABASE' as const },
  { foodName: 'Beer', averageShelfLifeDays: 180, category: 'beverages', confidence: 0.85, source: 'DATABASE' as const }
];

/**
 * Seed the database with common food shelf life data
 */
export const seedShelfLifeData = async (): Promise<void> => {
  try {
    console.log('Seeding shelf life data...');
    
    let seededCount = 0;
    let skippedCount = 0;

    for (const foodData of commonFoodShelfLife) {
      try {
        const result = await FoodShelfLife.createOrUpdate({
          ...foodData,
          storageConditions: getStorageConditions(foodData.category)
        });
        
        if (result) {
          seededCount++;
        }
      } catch (error: any) {
        console.warn(`Failed to seed ${foodData.foodName}:`, error.message);
        skippedCount++;
      }
    }

    console.log(`Shelf life data seeding completed: ${seededCount} seeded, ${skippedCount} skipped`);
  } catch (error: any) {
    console.error('Failed to seed shelf life data:', error.message);
    throw error;
  }
};

/**
 * Get appropriate storage conditions based on food category
 */
function getStorageConditions(category: string): string {
  const storageMap: Record<string, string> = {
    'fruits': 'Store in refrigerator crisper drawer or at room temperature depending on ripeness',
    'vegetables': 'Store in refrigerator crisper drawer at 4°C or below',
    'dairy': 'Store in refrigerator at 4°C or below',
    'meat': 'Store in refrigerator at 4°C or below, use within recommended time',
    'poultry': 'Store in refrigerator at 4°C or below, use within recommended time',
    'seafood': 'Store in refrigerator at 4°C or below, use within recommended time',
    'baked_goods': 'Store in cool, dry place or refrigerate for extended freshness',
    'herbs': 'Store in refrigerator, some herbs can be stored in water like flowers',
    'spices': 'Store in cool, dry place away from light',
    'grains': 'Store in cool, dry place in airtight containers',
    'legumes': 'Store in cool, dry place in airtight containers',
    'condiments': 'Store according to package instructions, refrigerate after opening',
    'beverages': 'Store in refrigerator or cool place as appropriate',
    'frozen': 'Store in freezer at -18°C or below',
    'canned': 'Store in cool, dry place, refrigerate after opening',
    'dried': 'Store in cool, dry place in airtight containers'
  };

  return storageMap[category] || 'Store in refrigerator at 4°C or below';
}
