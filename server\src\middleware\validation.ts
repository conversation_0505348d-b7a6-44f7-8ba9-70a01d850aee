import { Request, Response, NextFunction } from 'express';
import { z } from 'zod';

// Validation schemas
export const createFridgeItemSchema = z.object({
  body: z.object({
    name: z.string()
      .trim()
      .min(1, 'Item name is required')
      .max(100, 'Item name cannot exceed 100 characters'),
    quantity: z.number()
      .positive('Quantity must be positive')
      .refine((val) => Number(val.toFixed(2)) === val, {
        message: 'Quantity can have at most 2 decimal places'
      }),
    unit: z.enum(['pieces', 'kg', 'g', 'l', 'ml', 'cups', 'tbsp', 'tsp']),
    expiryDate: z.string()
      .refine((date) => {
        const expiryDate = new Date(date);
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(0, 0, 0, 0);
        return expiryDate >= tomorrow;
      }, {
        message: 'Expiry date must be at least tomorrow'
      }),
    isFavorite: z.boolean().optional().default(false)
  })
});

export const updateFridgeItemSchema = z.object({
  body: z.object({
    name: z.string()
      .trim()
      .min(1, 'Item name cannot be empty')
      .max(100, 'Item name cannot exceed 100 characters')
      .optional(),
    quantity: z.number()
      .positive('Quantity must be positive')
      .refine((val) => Number(val.toFixed(2)) === val, {
        message: 'Quantity can have at most 2 decimal places'
      })
      .optional(),
    unit: z.enum(['pieces', 'kg', 'g', 'l', 'ml', 'cups', 'tbsp', 'tsp']).optional(),
    expiryDate: z.string()
      .refine((date) => {
        const expiryDate = new Date(date);
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(0, 0, 0, 0);
        return expiryDate >= tomorrow;
      }, {
        message: 'Expiry date must be at least tomorrow'
      })
      .optional(),
    isFavorite: z.boolean().optional()
  })
});

export const fridgeItemsQuerySchema = z.object({
  query: z.object({
    page: z.string().regex(/^\d+$/).transform(Number).optional(),
    limit: z.string().regex(/^\d+$/).transform(Number).optional(),
    sortBy: z.enum(['expiryDate', 'createdAt', 'name']).optional(),
    sortOrder: z.enum(['asc', 'desc']).optional(),
    expiryStatus: z.enum(['expired', 'expires_today', 'expires_soon', 'expires_this_week', 'fresh']).optional(),
    isFavorite: z.string().transform((val) => val === 'true').optional(),
    search: z.string().optional()
  })
});

export const generateRecipeSchema = z.object({
  body: z.object({
    fridgeItemIds: z.array(z.string().min(1, 'Invalid fridge item ID'))
      .min(1, 'At least one fridge item ID is required')
      .max(20, 'Cannot generate recipe with more than 20 ingredients'),
    preferences: z.object({
      cuisine: z.string().optional(),
      difficulty: z.enum(['easy', 'medium', 'hard']).optional(),
      cookingTime: z.number().positive().max(480).optional(), // max 8 hours
      servings: z.number().positive().max(20).optional()
    }).optional()
  })
});

export const recipesQuerySchema = z.object({
  query: z.object({
    page: z.string().regex(/^\d+$/).transform(Number).optional(),
    limit: z.string().regex(/^\d+$/).transform(Number).optional(),
    sortBy: z.enum(['createdAt', 'title', 'ingredientCount']).optional(),
    sortOrder: z.enum(['asc', 'desc']).optional(),
    source: z.enum(['AI', 'DATABASE', 'USER']).optional(),
    isLiked: z.string().transform((val) => val === 'true').optional(),
    search: z.string().optional(),
    dateFrom: z.string().refine((date) => !isNaN(Date.parse(date)), {
      message: 'Invalid date format for dateFrom'
    }).optional(),
    dateTo: z.string().refine((date) => !isNaN(Date.parse(date)), {
      message: 'Invalid date format for dateTo'
    }).optional()
  })
});

export const mongoIdSchema = z.object({
  params: z.object({
    id: z.string().regex(/^[0-9a-fA-F]{24}$/, 'Invalid MongoDB ObjectId format')
  })
});

// Validation middleware factory
export const validate = (schema: z.ZodSchema<any>) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const result = schema.parse({
        body: req.body,
        query: req.query,
        params: req.params
      });

      // Update request objects with validated data
      if (result.body) req.body = result.body;
      if (result.query) req.query = result.query;
      if (result.params) req.params = result.params;

      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        const validationErrors = error.issues.map(issue => ({
          field: issue.path.join('.'),
          message: issue.message,
          code: issue.code
        }));

        res.status(400).json({
          success: false,
          statusCode: 400,
          message: 'Validation failed',
          errors: validationErrors,
          timestamp: new Date().toISOString()
        });
        return;
      }

      // Handle other validation errors
      res.status(500).json({
        success: false,
        statusCode: 500,
        message: 'Internal validation error',
        timestamp: new Date().toISOString()
      });
    }
  };
};

// Specific validation middleware
export const validateCreateFridgeItem = validate(createFridgeItemSchema);
export const validateUpdateFridgeItem = validate(updateFridgeItemSchema);
export const validateFridgeItemsQuery = validate(fridgeItemsQuerySchema);
export const validateGenerateRecipe = validate(generateRecipeSchema);
export const validateRecipesQuery = validate(recipesQuerySchema);
export const validateMongoId = validate(mongoIdSchema);

// Custom validation for pagination limits
export const validatePagination = (req: Request, res: Response, next: NextFunction) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;

  if (page < 1) {
    res.status(400).json({
      success: false,
      statusCode: 400,
      message: 'Page number must be greater than 0',
      timestamp: new Date().toISOString()
    });
    return;
  }

  if (limit < 1 || limit > 100) {
    res.status(400).json({
      success: false,
      statusCode: 400,
      message: 'Limit must be between 1 and 100',
      timestamp: new Date().toISOString()
    });
    return;
  }

  req.query.page = page.toString();
  req.query.limit = limit.toString();
  next();
};
