import cron from 'node-cron';
import { recipeGenerationQueue, cleanupQueue, shelfLifeQueue } from './queue';
import { queueDailyRecipeGeneration } from './processors/recipeGenerationProcessor';
import { queueUnknownFoodsProcessing } from './processors/shelfLifeProcessor';

/**
 * CRON job configurations
 */
const cronJobs = {
  // Daily recipe generation at 6:00 AM
  dailyRecipeGeneration: {
    schedule: '0 6 * * *', // Every day at 6:00 AM
    timezone: 'UTC',
    enabled: true,
    name: 'Daily Recipe Generation'
  },

  // Cleanup old jobs at 2:00 AM
  cleanup: {
    schedule: '0 2 * * *', // Every day at 2:00 AM
    timezone: 'UTC',
    enabled: true,
    name: 'Job Cleanup'
  },

  // Health check every 30 minutes
  healthCheck: {
    schedule: '*/30 * * * *', // Every 30 minutes
    timezone: 'UTC',
    enabled: true,
    name: 'Health Check'
  },

  // Batch shelf life processing at 3:00 AM
  shelfLifeProcessing: {
    schedule: '0 3 * * *', // Every day at 3:00 AM
    timezone: 'UTC',
    enabled: true,
    name: 'Shelf Life Processing'
  }
};

/**
 * Initialize and start all CRON jobs
 */
export const initializeCronJobs = async () => {
  console.log('Initializing CRON jobs...');

  // Daily recipe generation job
  if (cronJobs.dailyRecipeGeneration.enabled) {
    cron.schedule(
      cronJobs.dailyRecipeGeneration.schedule,
      async () => {
        console.log(`[CRON] Starting ${cronJobs.dailyRecipeGeneration.name}...`);
        try {
          await queueDailyRecipeGeneration();
          console.log(`[CRON] ${cronJobs.dailyRecipeGeneration.name} completed successfully`);
        } catch (error: any) {
          console.error(`[CRON] ${cronJobs.dailyRecipeGeneration.name} failed:`, error.message);
        }
      },
      {
        timezone: cronJobs.dailyRecipeGeneration.timezone as any
      }
    );
    console.log(`✓ ${cronJobs.dailyRecipeGeneration.name} scheduled: ${cronJobs.dailyRecipeGeneration.schedule}`);
  }

  // Cleanup job
  if (cronJobs.cleanup.enabled) {
    cron.schedule(
      cronJobs.cleanup.schedule,
      async () => {
        console.log(`[CRON] Starting ${cronJobs.cleanup.name}...`);
        try {
          await performCleanup();
          console.log(`[CRON] ${cronJobs.cleanup.name} completed successfully`);
        } catch (error: any) {
          console.error(`[CRON] ${cronJobs.cleanup.name} failed:`, error.message);
        }
      },
      {
        timezone: cronJobs.cleanup.timezone as any
      }
    );
    console.log(`✓ ${cronJobs.cleanup.name} scheduled: ${cronJobs.cleanup.schedule}`);
  }

  // Health check job
  if (cronJobs.healthCheck.enabled) {
    cron.schedule(
      cronJobs.healthCheck.schedule,
      async () => {
        try {
          await performHealthCheck();
        } catch (error: any) {
          console.error(`[CRON] ${cronJobs.healthCheck.name} failed:`, error.message);
        }
      },
      {
        timezone: cronJobs.healthCheck.timezone as any
      }
    );
    console.log(`✓ ${cronJobs.healthCheck.name} scheduled: ${cronJobs.healthCheck.schedule}`);
  }

  // Shelf life processing job
  if (cronJobs.shelfLifeProcessing.enabled) {
    cron.schedule(
      cronJobs.shelfLifeProcessing.schedule,
      async () => {
        console.log(`[CRON] Starting ${cronJobs.shelfLifeProcessing.name}...`);
        try {
          await queueUnknownFoodsProcessing();
          console.log(`[CRON] ${cronJobs.shelfLifeProcessing.name} completed successfully`);
        } catch (error: any) {
          console.error(`[CRON] ${cronJobs.shelfLifeProcessing.name} failed:`, error.message);
        }
      },
      {
        timezone: cronJobs.shelfLifeProcessing.timezone as any
      }
    );
    console.log(`✓ ${cronJobs.shelfLifeProcessing.name} scheduled: ${cronJobs.shelfLifeProcessing.schedule}`);
  }

  console.log('All CRON jobs initialized successfully');
};

/**
 * Perform cleanup of old jobs and data
 */
async function performCleanup() {
  try {
    // Clean up completed jobs older than 24 hours
    const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
    
    // Clean recipe generation queue
    await recipeGenerationQueue.clean(oneDayAgo, 'completed');
    await recipeGenerationQueue.clean(oneDayAgo, 'failed');
    
    console.log('Job queues cleaned successfully');

    // Add cleanup job to queue for database cleanup
    await cleanupQueue.add('database-cleanup', {
      timestamp: new Date().toISOString()
    }, {
      attempts: 2,
      backoff: {
        type: 'exponential',
        delay: 5000
      }
    });

  } catch (error: any) {
    console.error('Cleanup failed:', error.message);
    throw error;
  }
}

/**
 * Perform health check on job queues
 */
async function performHealthCheck() {
  try {
    // Check queue health
    const recipeQueueHealth = await recipeGenerationQueue.getJobCounts();
    
    // Log queue statistics
    console.log('[HEALTH] Recipe Generation Queue:', {
      waiting: recipeQueueHealth.waiting,
      active: recipeQueueHealth.active,
      completed: recipeQueueHealth.completed,
      failed: recipeQueueHealth.failed
    });

    // Alert if too many failed jobs
    if (recipeQueueHealth.failed > 50) {
      console.warn(`[HEALTH] High number of failed jobs in recipe generation queue: ${recipeQueueHealth.failed}`);
    }

    // Alert if queue is stalled
    if (recipeQueueHealth.active > 0 && recipeQueueHealth.waiting > 100) {
      console.warn(`[HEALTH] Recipe generation queue may be stalled. Active: ${recipeQueueHealth.active}, Waiting: ${recipeQueueHealth.waiting}`);
    }

  } catch (error: any) {
    console.error('Health check failed:', error.message);
  }
}

/**
 * Stop all CRON jobs (for graceful shutdown)
 */
export const stopCronJobs = () => {
  console.log('Stopping all CRON jobs...');
  cron.getTasks().forEach((task) => {
    task.stop();
  });
  console.log('All CRON jobs stopped');
};

/**
 * Get status of all CRON jobs
 */
export const getCronJobStatus = () => {
  const tasks = cron.getTasks();
  const status = Object.entries(cronJobs).map(([key, config]) => ({
    name: config.name,
    schedule: config.schedule,
    enabled: config.enabled,
    timezone: config.timezone,
    running: tasks.size > 0
  }));

  return {
    totalJobs: Object.keys(cronJobs).length,
    enabledJobs: Object.values(cronJobs).filter(job => job.enabled).length,
    jobs: status
  };
};

// Handle graceful shutdown
process.on('SIGTERM', () => {
  stopCronJobs();
  process.exit(0);
});

process.on('SIGINT', () => {
  stopCronJobs();
  process.exit(0);
});
