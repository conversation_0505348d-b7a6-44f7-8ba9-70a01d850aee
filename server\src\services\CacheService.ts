import { redis } from '../jobs/queue';
import { CacheOptions, CachedRecipe } from '../types';

export class CacheService {
  private defaultTTL = 24 * 60 * 60; // 24 hours in seconds

  /**
   * Cache a generated recipe
   */
  async cacheRecipe(ingredientHash: string, recipe: any, ttl?: number): Promise<boolean> {
    try {
      const cacheKey = `recipe:${ingredientHash}`;
      const cacheData: CachedRecipe = {
        recipe,
        cachedAt: new Date(),
        expiresAt: new Date(Date.now() + (ttl || this.defaultTTL) * 1000)
      };

      await redis.setex(cacheKey, ttl || this.defaultTTL, JSON.stringify(cacheData));
      console.log(`Recipe cached with key: ${cacheKey}`);
      return true;
    } catch (error: any) {
      console.error('Failed to cache recipe:', error.message);
      return false;
    }
  }

  /**
   * Get cached recipe by ingredient hash
   */
  async getCachedRecipe(ingredientHash: string): Promise<any | null> {
    try {
      const cacheKey = `recipe:${ingredientHash}`;
      const cachedData = await redis.get(cacheKey);
      
      if (!cachedData) {
        return null;
      }

      const parsedData: CachedRecipe = JSON.parse(cachedData);
      
      // Check if cache has expired (double check)
      if (new Date() > new Date(parsedData.expiresAt)) {
        await this.deleteCachedRecipe(ingredientHash);
        return null;
      }

      console.log(`Recipe retrieved from cache: ${cacheKey}`);
      return parsedData.recipe;
    } catch (error: any) {
      console.error('Failed to get cached recipe:', error.message);
      return null;
    }
  }

  /**
   * Delete cached recipe
   */
  async deleteCachedRecipe(ingredientHash: string): Promise<boolean> {
    try {
      const cacheKey = `recipe:${ingredientHash}`;
      const result = await redis.del(cacheKey);
      return result > 0;
    } catch (error: any) {
      console.error('Failed to delete cached recipe:', error.message);
      return false;
    }
  }

  /**
   * Cache user daily limits
   */
  async cacheUserLimit(userId: string, date: string, limitData: any, ttl?: number): Promise<boolean> {
    try {
      const cacheKey = `user_limit:${userId}:${date}`;
      const cacheExpiry = ttl || (24 * 60 * 60); // 24 hours
      
      await redis.setex(cacheKey, cacheExpiry, JSON.stringify(limitData));
      console.log('User limit cached', { userId: userId.slice(0, 6) + '…', date });
      return true;
    } catch (error: any) {
      console.error('Failed to cache user limit:', error.message);
      return false;
    }
  }

  /**
   * Get cached user daily limits
   */
  async getCachedUserLimit(userId: string, date: string): Promise<any | null> {
    try {
      const cacheKey = `user_limit:${userId}:${date}`;
      const cachedData = await redis.get(cacheKey);
      
      if (!cachedData) {
        return null;
      }

      return JSON.parse(cachedData);
    } catch (error: any) {
      console.error('Failed to get cached user limit:', error.message);
      return null;
    }
  }

  /**
   * Cache fridge items for a user
   */
  async cacheFridgeItems(userId: string, items: any[], ttl?: number): Promise<boolean> {
    try {
      const cacheKey = `fridge_items:${userId}`;
      const cacheExpiry = ttl || (30 * 60); // 30 minutes
      
      const cacheData = {
        items,
        cachedAt: new Date(),
        expiresAt: new Date(Date.now() + cacheExpiry * 1000)
      };

      await redis.setex(cacheKey, cacheExpiry, JSON.stringify(cacheData));
      return true;
    } catch (error: any) {
      console.error('Failed to cache fridge items:', error.message);
      return false;
    }
  }

  /**
   * Get cached fridge items
   */
  async getCachedFridgeItems(userId: string): Promise<any[] | null> {
    try {
      const cacheKey = `fridge_items:${userId}`;
      const cachedData = await redis.get(cacheKey);
      
      if (!cachedData) {
        return null;
      }

      const parsedData = JSON.parse(cachedData);
      
      // Check if cache has expired
      if (new Date() > new Date(parsedData.expiresAt)) {
        await redis.del(cacheKey);
        return null;
      }

      return parsedData.items;
    } catch (error: any) {
      console.error('Failed to get cached fridge items:', error.message);
      return null;
    }
  }

  /**
   * Invalidate fridge items cache for a user
   */
  async invalidateFridgeItemsCache(userId: string): Promise<boolean> {
    try {
      const cacheKey = `fridge_items:${userId}`;
      const result = await redis.del(cacheKey);
      return result > 0;
    } catch (error: any) {
      console.error('Failed to invalidate fridge items cache:', error.message);
      return false;
    }
  }

  /**
   * Cache user recommendations
   */
  async cacheRecommendations(userId: string, recommendations: any[], ttl?: number): Promise<boolean> {
    try {
      const cacheKey = `recommendations:${userId}`;
      const cacheExpiry = ttl || (60 * 60); // 1 hour
      
      await redis.setex(cacheKey, cacheExpiry, JSON.stringify(recommendations));
      return true;
    } catch (error: any) {
      console.error('Failed to cache recommendations:', error.message);
      return false;
    }
  }

  /**
   * Get cached recommendations
   */
  async getCachedRecommendations(userId: string): Promise<any[] | null> {
    try {
      const cacheKey = `recommendations:${userId}`;
      const cachedData = await redis.get(cacheKey);
      
      if (!cachedData) {
        return null;
      }

      return JSON.parse(cachedData);
    } catch (error: any) {
      console.error('Failed to get cached recommendations:', error.message);
      return null;
    }
  }

  /**
   * Generic cache set method
   */
  async set(key: string, value: any, ttl?: number): Promise<boolean> {
    try {
      const serializedValue = JSON.stringify(value);
      if (ttl) {
        await redis.setex(key, ttl, serializedValue);
      } else {
        await redis.set(key, serializedValue);
      }
      return true;
    } catch (error: any) {
      console.error(`Failed to cache key ${key}:`, error.message);
      return false;
    }
  }

  /**
   * Generic cache get method
   */
  async get(key: string): Promise<any | null> {
    try {
      const cachedData = await redis.get(key);
      if (!cachedData) {
        return null;
      }
      return JSON.parse(cachedData);
    } catch (error: any) {
      console.error(`Failed to get cached key ${key}:`, error.message);
      return null;
    }
  }

  /**
   * Delete cache key
   */
  async delete(key: string): Promise<boolean> {
    try {
      const result = await redis.del(key);
      return result > 0;
    } catch (error: any) {
      console.error(`Failed to delete cache key ${key}:`, error.message);
      return false;
    }
  }

  /**
   * Check if cache key exists
   */
  async exists(key: string): Promise<boolean> {
    try {
      const result = await redis.exists(key);
      return result === 1;
    } catch (error: any) {
      console.error(`Failed to check cache key ${key}:`, error.message);
      return false;
    }
  }

  /**
   * Set cache expiration
   */
  async expire(key: string, ttl: number): Promise<boolean> {
    try {
      const result = await redis.expire(key, ttl);
      return result === 1;
    } catch (error: any) {
      console.error(`Failed to set expiration for key ${key}:`, error.message);
      return false;
    }
  }

  /**
   * Get cache statistics
   */
  async getStats(): Promise<any> {
    try {
      const info = await redis.info('memory');
      const keyspace = await redis.info('keyspace');
      
      return {
        memory: info,
        keyspace: keyspace,
        timestamp: new Date().toISOString()
      };
    } catch (error: any) {
      console.error('Failed to get cache stats:', error.message);
      return null;
    }
  }

  /**
   * Clear cache with safety checks and prefix-based deletion
   * Only works in test environments or when explicitly allowed
   */
  async clearAll(prefix?: string): Promise<boolean> {
    try {
      // Environment safety check
      const nodeEnv = process.env.NODE_ENV;
      const allowCacheFlush = process.env.ALLOW_CACHE_FLUSH === 'true';
      const isTestEnv = nodeEnv === 'test' || nodeEnv === 'development';
      
      if (!isTestEnv && !allowCacheFlush) {
        const errorMsg = `Cache flush denied: NODE_ENV=${nodeEnv}, ALLOW_CACHE_FLUSH=${process.env.ALLOW_CACHE_FLUSH}. Only allowed in test/development environments or when ALLOW_CACHE_FLUSH=true`;
        console.error(errorMsg);
        throw new Error(errorMsg);
      }

      // Use prefix-scoped deletion instead of global flush
      const searchPrefix = prefix || 'recipe:*'; // Default to recipe prefix
      console.log(`Starting safe cache clear with prefix: ${searchPrefix}`);
      
      let cursor = '0';
      let deletedCount = 0;
      const batchSize = 100;

      do {
        // Use SCAN to find keys matching the prefix
        const result = await redis.scan(cursor, 'MATCH', searchPrefix, 'COUNT', batchSize);
        cursor = result[0];
        const keys = result[1];

        if (keys.length > 0) {
          // Delete keys in batch using pipeline for better performance
          const pipeline = redis.pipeline();
          keys.forEach(key => pipeline.unlink(key)); // UNLINK is non-blocking version of DEL
          await pipeline.exec();
          deletedCount += keys.length;
          console.log(`Deleted ${keys.length} keys (total: ${deletedCount})`);
        }
      } while (cursor !== '0');

      console.log(`Safe cache clear completed. Deleted ${deletedCount} keys with prefix: ${searchPrefix}`);
      return true;
    } catch (error: any) {
      const errorContext = {
        method: 'clearAll',
        prefix: prefix || 'recipe:*',
        nodeEnv: process.env.NODE_ENV,
        allowCacheFlush: process.env.ALLOW_CACHE_FLUSH,
        error: error.message
      };
      console.error('Failed to clear cache safely:', errorContext);
      return false;
    }
  }
}
