
//import { registerForPushNotificationsAsync } from "@/app/_layout";

import { router } from "expo-router";
import * as SecureStore from "expo-secure-store";
import { Alert, Platform } from "react-native";


//const API_URL = "http://localhost:4000"
const API_URL = "http://*********:4000"
//const API_URL = "http://************:4000"

const TOKEN_KEY = "hazelchef_token";



//GLOBAL CALLS

export async function getToken() {
    const token = await SecureStore.getItemAsync(TOKEN_KEY);

    if (token) {
        return token
    } else {
        return false
    }
}
export async function setToken(token: string) {
    try {
        if (!token) throw new Error("Invalid token");
        await SecureStore.setItemAsync(TOKEN_KEY, token);
    } catch (error) {
        console.error("Error setting token:", error);
    }
}
export async function removeToken() {
    //delete from secure store
    await SecureStore.deleteItemAsync(TOKEN_KEY);

    //redirect to main selector screen
    router.replace('/auth/login')
}


/* AUTH CALLS */

export async function authenticateOAuth() {

    const token = await getToken()

    if (token) {
        const call = await fetch(`${API_URL}/oauth/authenticate`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            }
        })

        const response = await call.json()
        
        if (!response.success) {
            return { success: false }
        }

        if (Platform.OS == 'ios') {
            //const saveNotificationToken = registerForPushNotificationsAsync()
        }

        return {
            success: true,
            data: response.data,
            subscription: response.subscription
        }
    } else {
        return { success: false }
    }
}
export async function appleLogin(identityToken: string) {

    const response = await fetch(`${API_URL}/oauth/login/apple`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify({
            identityToken,
        })
    })


    return await response.json()
}
export async function googleLogin(id: string, email: string, name: string, surname: string, photo: string) {

    const response = await fetch(`${API_URL}/oauth/login/google`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify({
            id,
            email,
            name,
            surname,
            photo
        })
    })


    return await response.json()
}



/* NOTIFICATIONS  */


export async function saveNotificationToken(expoToken: string){
        const token = await getToken()
        if (!token) return

        const response = await fetch(`${API_URL}/notifications/save-token`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${token}`

        },
        body: JSON.stringify({
            token: expoToken
        })
    })


    return await response.json()
}

