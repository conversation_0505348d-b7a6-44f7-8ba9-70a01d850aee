'use client'

import { Refrigerator } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'

const BottomNav = () => {
	

	return (
		<div className='sm:hidden'>
			<nav className='fixed bottom-0 w-full border-t bg-white pb-safe dark:border-zinc-100 dark:bg-[#eaeaea95]'
				style={{
					backdropFilter: 'blur(10px)',
					WebkitBackdropFilter: 'blur(10px)',
					backgroundColor: 'rgba(255, 255, 255, 1)',
					boxShadow: '0 -1px 2px rgba(0, 0, 0, 0.1)',
					transition: 'background-color 0.3s ease-in-out',
					borderRadius: '20px 20px 0 0',
				}}
				>
				<div className='mx-auto flex h-16 max-w-md items-center justify-around px-6'>
					{links.map(({ href, label, icon }) => (
						<Link
							key={label}
							href={href}
							className={`flex h-full w-full flex-col items-center justify-center space-y-1`}
						/* ${
							router.pathname === href
								? 'text-indigo-500 dark:text-indigo-400'
								: 'text-zinc-600 hover:text-zinc-900 dark:text-zinc-400 dark:hover:text-zinc-50'
						} */
						>
							{icon}
							<span className='text-xs text-zinc-600 dark:text-zinc-400'>
								{label}
							</span>
						</Link>
					))}
				</div>
			</nav>
		</div>
	)
}

export default BottomNav

const links = [
	{
		label: 'Home',
		href: '/home',
		icon: (
			<svg xmlns="http://www.w3.org/2000/svg" fill="none" color='#959595' viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="size-6">
				<path strokeLinecap="round" strokeLinejoin="round" d="m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
			</svg>

		),
	},
	{
		label: 'Fridge',
		href: '/fridge',
		icon: (
			<Refrigerator color='#959595' strokeWidth={1.4}/>

		),
	},
	{
		label: 'Settings',
		href: '/settings',
		icon: (
			<svg xmlns="http://www.w3.org/2000/svg" color='#959595' fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="size-6">
				<path strokeLinecap="round" strokeLinejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z" />
				<path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
			</svg>


		),
	},
]
