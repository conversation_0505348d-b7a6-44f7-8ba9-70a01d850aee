import { Job } from 'bull';
import mongoose from 'mongoose';
import { FridgeItem, Account, UserRecipeUsage } from '../../database/schemas';
import { recipeGenerationQueue } from '../queue';
import { RecipeService } from '../../services';
import { RecipeGenerationJobData, UserTier } from '../../types';
import { notificationQueue } from '../queue';

const recipeService = new RecipeService();

export interface RecipeGenerationJobPayload extends RecipeGenerationJobData {
  userId: string;
  expiringItemIds: string[];
  userTier: UserTier;
  jobType: 'daily_auto' | 'manual';
}

/**
 * Process recipe generation jobs
 */
export const processRecipeGeneration = async (job: Job<RecipeGenerationJobPayload>) => {
  const { userId, expiringItemIds, userTier, jobType } = job.data;

  try {
    // Update job progress
    await job.progress(10);

    console.log(`Processing recipe generation for user ${userId}, items: ${expiringItemIds.length}`);

    // Verify user exists and get user info
    const user = await Account.findById(userId).lean();
    if (!user) {
      throw new Error(`User ${userId} not found`);
    }

    await job.progress(20);

    // Check if user has reached daily limit (for auto-generated recipes)
    if (jobType === 'daily_auto') {
      const usage = await UserRecipeUsage.getOrCreateTodayUsage(
        new mongoose.Types.ObjectId(userId),
        userTier
      );

      const dailyLimit = userTier === 'PRO' ? 15 : 5;
      if (usage.generatedCount >= dailyLimit) {
        console.log(`User ${userId} has reached daily limit, skipping auto-generation`);
        return { success: false, reason: 'Daily limit reached' };
      }
    }

    await job.progress(30);

    // Verify fridge items still exist and belong to user
    const fridgeItems = await FridgeItem.find({
      _id: { $in: expiringItemIds.map(id => new mongoose.Types.ObjectId(id)) },
      userId: new mongoose.Types.ObjectId(userId)
    }).lean();

    if (fridgeItems.length === 0) {
      console.log(`No valid fridge items found for user ${userId}`);
      return { success: false, reason: 'No valid fridge items' };
    }

    await job.progress(50);

    // Generate recipe using the service
    const result = await recipeService.generateRecipe(userId, expiringItemIds, userTier);

    if (!result.success) {
      throw new Error(`Recipe generation failed: ${result.error}`);
    }

    await job.progress(80);

    // Send notification to user about the new recipe
    const notificationPayload = {
      userId,
      type: 'recipe_generated',
      title: 'New Recipe Generated!',
      body: `We've created a delicious recipe using your expiring ingredients: ${result.data.recipe.title}`,
      data: {
        recipeId: result.data.recipe.id,
        recipeTitle: result.data.recipe.title,
        ingredientCount: result.data.recipe.ingredientCount,
        jobType
      }
    };

    // Queue notification job
    await notificationQueue.add('send-push-notification', notificationPayload, {
      delay: 1000, // Send notification after 1 second
      attempts: 3
    });

    await job.progress(100);

    console.log(`Recipe generation completed for user ${userId}: ${result.data.recipe.title}`);

    return {
      success: true,
      recipeId: result.data.recipe.id,
      recipeTitle: result.data.recipe.title,
      usageInfo: result.data.usageInfo,
      notificationSent: true
    };

  } catch (error: any) {
    console.error(`Recipe generation job failed for user ${userId}:`, error.message);

    // Send error notification to user (optional)
    if (jobType === 'manual') {
      const errorNotificationPayload = {
        userId,
        type: 'recipe_generation_failed',
        title: 'Recipe Generation Failed',
        body: 'We encountered an issue generating your recipe. Please try again later.',
        data: {
          error: error.message,
          jobType
        }
      };

      await notificationQueue.add('send-push-notification', errorNotificationPayload, {
        attempts: 2
      });
    }

    throw error;
  }
};

/**
 * Find users with expiring items and queue recipe generation jobs
 */
export const queueDailyRecipeGeneration = async () => {
  try {
    console.log('Starting daily recipe generation job queuing...');

    const today = new Date();
    today.setUTCHours(0, 0, 0, 0);

    const twoDaysFromNow = new Date(today);
    twoDaysFromNow.setUTCDate(twoDaysFromNow.getUTCDate() + 2);

    const threeDaysFromNow = new Date(today);
    threeDaysFromNow.setUTCDate(threeDaysFromNow.getUTCDate() + 3);

    // Find users with items expiring in 2-3 days
    const usersWithExpiringItems = await FridgeItem.aggregate([
      {
        $match: {
          expiryDate: {
            $gte: twoDaysFromNow,
            $lte: threeDaysFromNow
          }
        }
      },
      {
        $group: {
          _id: '$userId',
          expiringItems: {
            $push: {
              itemId: '$_id',
              name: '$name',
              expiryDate: '$expiryDate',
              isFavorite: '$isFavorite'
            }
          },
          count: { $sum: 1 }
        }
      },
      {
        $match: {
          count: { $gte: 2 } // Only users with at least 2 expiring items
        }
      }
    ]);

    console.log(`Found ${usersWithExpiringItems.length} users with expiring items`);

    let jobsQueued = 0;

    for (const userGroup of usersWithExpiringItems) {
      const userId = userGroup._id.toString();

      // Get user info to determine tier
      const user = await Account.findById(userId).lean();
      if (!user) continue;

      const userTier: UserTier = user.finances?.subscription_plan === 'free' ? 'FREE' : 'PRO';

      // Check if user hasn't reached daily limit
      const usage = await UserRecipeUsage.getOrCreateTodayUsage(
        new mongoose.Types.ObjectId(userId),
        userTier
      );

      const dailyLimit = userTier === 'PRO' ? 15 : 5;
      if (usage.generatedCount >= dailyLimit) {
        console.log(`User ${userId} has reached daily limit, skipping`);
        continue;
      }

      // Select up to 5 expiring items (prioritize favorites)
      const sortedItems = userGroup.expiringItems
        .sort((a: any, b: any) => {
          if (a.isFavorite && !b.isFavorite) return -1;
          if (!a.isFavorite && b.isFavorite) return 1;
          return new Date(a.expiryDate).getTime() - new Date(b.expiryDate).getTime();
        })
        .slice(0, 5);

      const expiringItemIds = sortedItems.map((item: any) => item.itemId.toString());

      // Queue recipe generation job
      const jobPayload: RecipeGenerationJobPayload = {
        userId,
        expiringItemIds,
        userTier,
        jobType: 'daily_auto'
      };

      await recipeGenerationQueue.add('generate-recipe', jobPayload, {
        delay: Math.random() * 30000, // Random delay up to 30 seconds to spread load
        attempts: 2,
        backoff: {
          type: 'exponential',
          delay: 5000
        }
      });

      jobsQueued++;
    }

    console.log(`Queued ${jobsQueued} daily recipe generation jobs`);
    return { success: true, jobsQueued };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error('Failed to queue daily recipe generation jobs:', errorMessage);
    throw error;
  }
};
