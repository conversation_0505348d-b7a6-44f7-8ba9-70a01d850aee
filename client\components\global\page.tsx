'use client'

import Head from 'next/head'
import Appbar from './appbar'
import BottomNav from './bottom-nav'
import Appbar<PERSON>ustom from './appbar-custom'

interface Props {
	title?: string
	children: React.ReactNode

	noPadding?: boolean
	goBack?: () => void
}

const Page = ({ title, children, goBack, noPadding }: Props) => (
	<>
		{title ? (
			<Head>
				<title>Rice Bowl | {title}</title>
			</Head>
		) : null}


		<Appbar />

		<main
			/**
			 * Padding top = `appbar` height
			 * Padding bottom = `bottom-nav` height
			*/
			className={`mx-auto max-w-screen-md pb-16 px-safe sm:pb-0 ${noPadding ? 'pt-6' : 'pt-20'}`}
		>
			<div className='p-6'>{children}</div>
		</main>
		<BottomNav />
	</>
)

export default Page
