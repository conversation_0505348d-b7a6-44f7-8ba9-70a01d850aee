'use client'


import Page from "@/components/global/page";
import Section from "@/components/global/section";
import CustomSearchSelect from "@/components/inputs/ingredient-input";


import Image from "next/image";
import { useState } from "react";

interface Item {
  id: string
  label: string
}

export default function Home() {
  const [selectedItems, setSelectedItems] = useState<Item[]>([])
  const [items, setItems] = useState<Item[]>([
    { id: "1", label: "Apple" },
    { id: "2", label: "Banana" },
    { id: "3", label: "Cherry" },
    { id: "4", label: "Durian" },
    { id: "5", label: "Elderberry" },
    { id: "6", label: "Fig" },
    { id: "7", label: "Grape" },
    { id: "8", label: "Honeydew" },
    { id: "9", label: "Kiwi" },
    { id: "10", label: "Lemon" },
  ])


  const handleSelect = (newSelectedItems: Item[]) => {
    setSelectedItems(newSelectedItems)
  }

  const handleAddNew = (label: string) => {
    const newItem = {
      id: `${items.length + 1}-${Date.now()}`,
      label,
    }
    setItems((prevItems) => [...prevItems, newItem])
    setSelectedItems((prevItems) => [...prevItems, newItem])
  }
  return (
    <Page>
      <Section>

        {/* <FloatingBackground/> */}
        <div style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          height: "550px",


        }}>
          <div
            className="mt-6 w-full px-2"
            style={{
              opacity: selectedItems.length > 0 ? 0 : 1,
              transition: "opacity 0.3s ease-in-out",
              color: '#00000050'
            }}
          >
            <h2 className='text-xl font-semibold'>
              Let&apos;s cook something amazing!
            </h2>
            <p className=''>
              Select what{' '}
              <span className='font-medium text-zinc-900 dark:text-zinc-700'>
                you want
              </span>{' '}
              to eat from your fridge
            </p>
          </div>
          <Image
            src="/logo/leftover-chef_green.png"
            alt="Leftover Chef Logo"
            width={400}
            height={400}
          />
          <CustomSearchSelect
            items={items}
            placeholder="Search or add items..."
            onSelect={handleSelect}
            onAddNew={handleAddNew}
            selectedItems={selectedItems}
          />
          <button
            className={`
            mt-4 h-[40px] flex w-full bg-green-500
            rounded-full overflow-hidden
            transition-all duration-150 ease-in-out items-center justify-center
          `}
            style={{
              opacity: selectedItems.length > 0 ? 1 : 0,
              transition: "opacity 0.3s ease-in-out",
              boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
            }}
          >

            <Image
              src="/logo/hazel-logo-white.png"
              alt="Leftover Chef Logo"
              width={20}
              height={20}
              className="ml-2"
            />
          </button>
        </div>

      </Section>
    </Page>
  );
}
