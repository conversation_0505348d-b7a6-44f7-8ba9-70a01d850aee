"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { motion, AnimatePresence } from "framer-motion"

interface MobileModalProps {
  isOpen: boolean
  onClose: () => void
  children: React.ReactNode
  height?: string | number
}

export default function MobileModal({ isOpen, onClose, children, height = "50%" }: MobileModalProps) {
  const [isMounted, setIsMounted] = useState(false)

  // Handle escape key press
  useEffect(() => {
    setIsMounted(true)

    const handleEscapeKey = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener("keydown", handleEscapeKey)
      // Prevent body scrolling when modal is open
      document.body.style.overflow = "hidden"
    }

    return () => {
      document.removeEventListener("keydown", handleEscapeKey)
      document.body.style.overflow = "auto"
    }
  }, [isOpen, onClose])

  // Don't render on the server to avoid hydration issues
  if (!isMounted) return null

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            className="fixed inset-0 bg-black/50 z-40 touch-none"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            className="fixed bottom-0 left-0 right-0 z-50 bg-white rounded-t-2xl shadow-lg overflow-hidden"
            style={{ height }}
            initial={{ y: "100%" }}
            animate={{
              y: 0,
              transition: {
                type: "spring",
                damping: 25,
                stiffness: 300,
              },
            }}
            exit={{
              y: "100%",
              transition: {
                type: "spring",
                damping: 25,
                stiffness: 300,
              },
            }}
            drag="y"
            dragConstraints={{ top: 0 }}
            dragElastic={0.2}
            onDragEnd={(_, info) => {
              if (info.offset.y > 100) {
                onClose()
              }
            }}
          >
            {/* Drag handle */}
            <div className="w-full flex justify-center pt-2 pb-4">
              <div className="w-10 h-1 bg-gray-300 rounded-full" />
            </div>

            {/* Content */}
            <div className="px-4 pb-8 overflow-y-auto max-h-[calc(100%-24px)]">{children}</div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  )
}
