import React, { useContext, useEffect, useRef, useState } from 'react'
import { Animated, Image, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native'
import { Ionicons } from "@expo/vector-icons"

import Page from '@/components/templates/Page'
import { ScrollView } from 'react-native-gesture-handler'
import Header from '@/components/templates/Header'
import ButtonGlobal from '@/components/atoms/buttons/ButtonGlobal'
import { AuthContext } from '@/context/AuthContext'





const Index = () => {
  const { userData } = useContext(AuthContext)


  return (
    <Page noPaddingTop alignItems='center' justifyContent='space-between' >
      <Header buttonBack text=' ' />

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>


      </ScrollView>
    </Page>
  )
}


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",

    paddingVertical: 16,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e9ecef",
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: "600",
    textAlign: "center",
    color: "#333",
  },
  editButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  editButtonText: {
    fontSize: 16,
    color: "rgba(34,197,94,0.7)",
    fontWeight: "500",
  },
  scrollView: {
    flex: 1,
    width: '100%'
  },
  photoSection: {
    backgroundColor: "#fff",
    paddingVertical: 40,
    paddingHorizontal: 24,

    alignItems: "center",
    marginBottom: 0,
  },
  photoContainer: {
    marginBottom: 16,
  },
  photoWrapper: {
    position: "relative",
  },
  photoPlaceholder: {
    width: 190,
    height: 190,
    borderRadius: 60,
    backgroundColor: "#f8f9fa",
    borderWidth: 4,
    borderColor: "rgba(34,197,94,0.7)",
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "rgba(34,197,94,0.7)",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  photoOverlay: {
    position: "absolute",
    bottom: 0,
    right: 0,
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: "rgba(34,197,94,0.7)",
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 3,
    borderColor: "#fff",
  },
  photoHint: {
    fontSize: 14,
    color: "#666",
    marginBottom: 8,
  },
  memberSince: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
  },
  memberSinceText: {
    fontSize: 12,
    color: "#666",
  },
  fieldsContainer: {
    marginTop: 100,
    paddingHorizontal: 0,
    gap: 16,
  },
  fieldCard: {
    backgroundColor: "#fff",
    borderRadius: 12,
    paddingHorizontal: 20,
    marginBottom: 10
  },
  fieldHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
    gap: 12,
  },
  fieldLabel: {
    fontSize: 14,
    fontWeight: "600",
    color: "#333",
    flex: 1,
  },
  fieldValue: {
    fontSize: 16,
    color: "#333",
    fontWeight: "500",
  },
  fieldInput: {
    fontSize: 16,
    color: "#333",
    fontWeight: "500",
    borderBottomWidth: 1,
    borderBottomColor: "#e9ecef",
    paddingBottom: 8,
  },
  fieldHint: {
    fontSize: 12,
    color: "#999",
    marginTop: 8,
  },
  timezoneSelector: {
    marginTop: 16,
    borderTopWidth: 1,
    borderTopColor: "#e9ecef",
    paddingTop: 16,
  },
  timezoneOption: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 8,
  },
  selectedTimezone: {
    backgroundColor: "#f0f9f0",
  },
  timezoneText: {
    fontSize: 14,
    color: "#333",
    flex: 1,
  },
  selectedTimezoneText: {
    color: "rgba(34,197,94,0.7)",
    fontWeight: "500",
  },
  saveSection: {
    padding: 20,
  },
  saveButton: {
    backgroundColor: "rgba(34,197,94,0.7)",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
  },
  saveButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  actionsSection: {
    marginTop: 20,
    paddingBottom: 32,
    gap: 12,
  },
  actionButton: {
    backgroundColor: "#fff",
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    gap: 12,
  },
  actionButtonText: {
    fontSize: 16,
    color: "#333",
    flex: 1,
  },
  dangerButton: {
    marginTop: 8,
  },
  dangerText: {
    color: "#F44336",
  },
})

export default Index

