import React, { useContext, useEffect, useState } from 'react';

import { router } from 'expo-router';
import Constants from 'expo-constants';
import { Image, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

import { triggerSelectionHaptic } from '@/utils/Haptics';
import PreferencesManager from '@/utils/LocalSettings';

import Page from '@/components/templates/Page';
import Header from '@/components/templates/Header';
import { ScreenWrapper } from '@/components/atoms/blur/BlurDrawer';

import { removeToken } from '@/services/api';
import { AuthContext } from '@/context/AuthContext';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useSubscriptions } from '@/context/SubcriptionsContext';
import { Ionicons } from '@expo/vector-icons';
import ArrowSquare from '@/components/atoms/icons/ArrowSquare';
import Crown from '@/components/atoms/icons/Crown';
import SpinnerLoaderSmall from '@/components/atoms/loaders/SpinnerSmall';
import ButtonSettings from '@/components/atoms/buttons/ButtonSettings';
import ButtonSettingsToggle from '@/components/atoms/buttons/ButtonSettingsToggle';
import i18n from '@/i18n/i18n';


const Index = () => {
  const version = Constants;
  const insets = useSafeAreaInsets();
  const { userData } = useContext(AuthContext);
  const { currentSubscription } = useSubscriptions();
  const [haptics, setHaptics] = useState<any>(false);
  const [notifications, setNotifications] = useState<any>(true);


  /* User Picture Logics */
  const [isImageLoading, setIsImageLoading] = useState(true);
  useEffect(() => {
    if (userData?.user.profile_picture) {
      Image.prefetch(userData.user.profile_picture)
        .then(() => setIsImageLoading(false))
        .catch(() => setIsImageLoading(false));
    } else {
      setIsImageLoading(false);
    }
  }, [userData?.user.profile_picture]);

  /* Initialize PreferencesManager and sync states */
  useEffect(() => {
    const initializePrefs = async () => {
      try {
        const manager = PreferencesManager.getInstance(); // Use singleton
        await manager.initialize();

        // Set initial states
        setHaptics(await manager.getHapticPreference());
        setNotifications(await manager.getNotificationPreference());

        // Subscribe to preference changes
        manager.on('hapticPreferenceChanged', (newValue: any) => {
          setHaptics(newValue);
        });
        manager.on('notificationPreferenceChanged', (newValue: any) => {
          setNotifications(newValue);
        });
      } catch (error) {
        console.error('Failed to initialize preferences:', error);
      }
    };

    initializePrefs();

    // Cleanup not needed for singleton Map-based listeners
    return () => { };
  }, []);

  /* Handle toggle actions with haptic feedback */
  const handleToggleHaptics = async () => {
    try {
      const manager = PreferencesManager.getInstance();
      await manager.toggleHapticPreference();
      await triggerSelectionHaptic(); // Trigger haptic feedback if enabled
    } catch (error) {
      console.error('Failed to toggle haptics:', error);
    }
  };
  const handleToggleNotifications = async () => {
    try {
      const manager = PreferencesManager.getInstance();
      await manager.toggleNotificationPreference();
    } catch (error) {
      console.error('Failed to toggle notifications:', error);
    }
  };
  

  return (
    <ScreenWrapper>
      <Page noPaddingTop alignItems="center" justifyContent="space-between" page="settings">
        <Header burgerMenu text=" " />
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{
            paddingTop: insets.top + 20,
            paddingHorizontal: 20,
          }}
        >
          {/* Profile Data */}
          <View
            style={{ flexDirection: 'column', alignItems: 'center', justifyContent: 'flex-start', gap: 20, width: '100%', marginTop: 30 }}
          >
            <TouchableOpacity style={{ width: 90, height: 90, alignItems: 'center', justifyContent: 'center' }}>
              <Image
                source={userData?.user.profile_picture ? { uri: userData.user.profile_picture } : require('@/assets/fallbacks/pfp.webp')}
                style={{ height: '100%', width: '100%', borderRadius: 50, opacity: isImageLoading ? 0 : 1 }}
              />
              {isImageLoading && (
                <View style={{ height: '100%', width: '100%', position: 'absolute', alignItems: 'center', justifyContent: 'center' }}>
                  <SpinnerLoaderSmall />
                </View>
              )}
            </TouchableOpacity>

            <View style={{ gap: 5, alignItems: 'center', justifyContent: 'flex-start', flexDirection: 'column', width: '100%' }}>
              <Text style={{ fontSize: 22, fontWeight: 500, textTransform: 'capitalize' }}>{userData?.user.name ?? 'User'}</Text>
              <View style={styles.memberSince}>
                <Ionicons name="calendar" size={16} color="#666" />
                <View style={{flexDirection: 'row', gap: 0}}>

                  <Text style={styles.memberSinceText}>
                    {i18n.t('settings.memberSince')}{' '}
                  </Text>
                  <Text style={[styles.memberSinceText,{textTransform: 'capitalize'}]}>
                    {new Date(userData?.createdAt ?? '').toLocaleDateString(i18n.t('common.id'),{ month: 'long', year: 'numeric' })}
                  </Text>
                </View>
              </View>
            </View>
          </View>

          {/* Container Buttons */}
          <View style={{ marginTop: 50 }}>

            {/* ACCOUNT */}
            <View style={{ marginBottom: 40 }}>
              <Text style={{ color: '#********', marginBottom: 15, textTransform: 'uppercase' }}>{i18n.t('settings.account')}</Text>
              <View>
                <ButtonSettings
                  icon={<Ionicons name="man-outline" size={18} />}
                  text={i18n.t('settings.account')}
                  onPress={() => router.push('/settings/account')}
                />
                <ButtonSettings
                  icon={<Ionicons name="shield-outline" size={18} />}
                  text={i18n.t('settings.security')}
                  onPress={() => { router.push('/settings/security') }}
                />
                <ButtonSettings
                  icon={<Ionicons name="disc-outline" size={18} />}
                  text={i18n.t('settings.subscription')}
                  noChevron
                  extra={
                    <>
                      {
                        currentSubscription == 'free' ?
                          <View style={{ paddingVertical: 6, paddingHorizontal: 15, backgroundColor: 'rgba(34,197,94,0.7)', borderRadius: 20, position: 'relative' }}>
                            <Text style={{ textAlign: 'center', color: 'white' }}>Free</Text>
                          </View>
                          : <View style={{ paddingVertical: 6, paddingHorizontal: 15, backgroundColor: 'orange', borderRadius: 20, position: 'relative' }}>
                            <Crown
                              style={{
                                position: 'absolute',
                                top: -10,
                                right: -5.5,
                                transform: [{ rotate: '35deg' }],
                              }}
                              color="orange"
                            />
                            <Text style={{ textAlign: 'center', color: 'white' }}>PRO</Text>
                          </View>
                      }
                    </>
                  }
                  onPress={() => router.push('/settings/subscriptions')}
                />
              </View>
            </View>

            {/* GENERAL */}
            <View style={{ marginBottom: 40 }}>
              <Text style={{ color: '#********', marginBottom: 15, textTransform: 'uppercase' }}>{i18n.t('settings.general')}</Text>
              <View>
                <ButtonSettings icon={<Ionicons name="fast-food-outline" size={18} />} text={i18n.t('settings.foodprefs')} onPress={() => { router.push('/settings/food') }} />
                <ButtonSettingsToggle icon={<Ionicons name="radio-button-on-outline" size={18} />} value={haptics} onClick={handleToggleHaptics} text={i18n.t('settings.haptics')} />
                <ButtonSettingsToggle icon={<Ionicons name="notifications-outline" size={18} />} value={notifications} onClick={handleToggleNotifications} text={i18n.t('settings.notifications')} />
                <ButtonSettings icon={<Ionicons name="language-outline" size={18} />} text={i18n.t('settings.language')} onPress={() => { router.push('/settings/language') }} />
              </View>
            </View>

            {/* INFO */}
            <View style={{ marginBottom: 40 }}>
              <Text style={{ color: '#********', marginBottom: 15, textTransform: 'uppercase' }}>{i18n.t('settings.info')}</Text>
              <View>
                <ButtonSettings
                  icon={<Ionicons name="reader-outline" size={18} />}
                  text={i18n.t('settings.t&s')}
                  onPress={() => router.push('/settings/terms')}
                />
                <ButtonSettings
                  icon={<Ionicons name="lock-closed-outline" size={18} />}
                  text={i18n.t('settings.privacypolicy')}
                  onPress={() => router.push('/settings/policy')}
                />
                <ButtonSettings
                  icon={<Ionicons name="phone-portrait-outline" size={18} />}
                  text={i18n.t('settings.version')}
                  noChevron
                  extra={
                    <Text style={{ color: '#********' }}>
                      v{version.expoConfig?.version} ({version.expoConfig?.ios?.buildNumber ?? 'internal'})
                    </Text>
                  }
                  onPress={() => router.push('/settings/version')}
                />
                <ButtonSettings
                  text={i18n.t('settings.logout')}
                  noChevron
                  icon={<ArrowSquare color="tomato" />}
                  iconStyle={{
                    backgroundColor: '#e03d0740',
                    borderColor: '#e03d0740',
                  }}
                  onPress={() => removeToken()}
                />
              </View>
            </View>

          </View>
        </ScrollView>
      </Page>
    </ScreenWrapper>
  );
};

export default Index;

const styles = StyleSheet.create({
  memberSince: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  memberSinceText: {
    fontSize: 12,
    color: '#666',
  },
});