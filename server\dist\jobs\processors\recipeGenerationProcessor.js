"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.queueDailyRecipeGeneration = exports.processRecipeGeneration = void 0;
const tslib_1 = require("tslib");
const mongoose_1 = tslib_1.__importDefault(require("mongoose"));
const schemas_1 = require("../../database/schemas");
const queue_1 = require("../queue");
const services_1 = require("../../services");
const queue_2 = require("../queue");
const recipeService = new services_1.RecipeService();
/**
 * Process recipe generation jobs
 */
const processRecipeGeneration = (job) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    const { userId, expiringItemIds, userTier, jobType } = job.data;
    try {
        // Update job progress
        yield job.progress(10);
        console.log(`Processing recipe generation for user ${userId}, items: ${expiringItemIds.length}`);
        // Verify user exists and get user info
        const user = yield schemas_1.Account.findById(userId).lean();
        if (!user) {
            throw new Error(`User ${userId} not found`);
        }
        yield job.progress(20);
        // Check if user has reached daily limit (for auto-generated recipes)
        if (jobType === 'daily_auto') {
            const usage = yield schemas_1.UserRecipeUsage.getOrCreateTodayUsage(new mongoose_1.default.Types.ObjectId(userId), userTier);
            const dailyLimit = userTier === 'PRO' ? 15 : 5;
            if (usage.generatedCount >= dailyLimit) {
                console.log(`User ${userId} has reached daily limit, skipping auto-generation`);
                return { success: false, reason: 'Daily limit reached' };
            }
        }
        yield job.progress(30);
        // Verify fridge items still exist and belong to user
        const fridgeItems = yield schemas_1.FridgeItem.find({
            _id: { $in: expiringItemIds.map(id => new mongoose_1.default.Types.ObjectId(id)) },
            userId: new mongoose_1.default.Types.ObjectId(userId)
        }).lean();
        if (fridgeItems.length === 0) {
            console.log(`No valid fridge items found for user ${userId}`);
            return { success: false, reason: 'No valid fridge items' };
        }
        yield job.progress(50);
        // Generate recipe using the service
        const result = yield recipeService.generateRecipe(userId, expiringItemIds, userTier);
        if (!result.success) {
            throw new Error(`Recipe generation failed: ${result.error}`);
        }
        yield job.progress(80);
        // Send notification to user about the new recipe
        const notificationPayload = {
            userId,
            type: 'recipe_generated',
            title: 'New Recipe Generated!',
            body: `We've created a delicious recipe using your expiring ingredients: ${result.data.recipe.title}`,
            data: {
                recipeId: result.data.recipe.id,
                recipeTitle: result.data.recipe.title,
                ingredientCount: result.data.recipe.ingredientCount,
                jobType
            }
        };
        // Queue notification job
        yield queue_2.notificationQueue.add('send-push-notification', notificationPayload, {
            delay: 1000, // Send notification after 1 second
            attempts: 3
        });
        yield job.progress(100);
        console.log(`Recipe generation completed for user ${userId}: ${result.data.recipe.title}`);
        return {
            success: true,
            recipeId: result.data.recipe.id,
            recipeTitle: result.data.recipe.title,
            usageInfo: result.data.usageInfo,
            notificationSent: true
        };
    }
    catch (error) {
        console.error(`Recipe generation job failed for user ${userId}:`, error.message);
        // Send error notification to user (optional)
        if (jobType === 'manual') {
            const errorNotificationPayload = {
                userId,
                type: 'recipe_generation_failed',
                title: 'Recipe Generation Failed',
                body: 'We encountered an issue generating your recipe. Please try again later.',
                data: {
                    error: error.message,
                    jobType
                }
            };
            yield queue_2.notificationQueue.add('send-push-notification', errorNotificationPayload, {
                attempts: 2
            });
        }
        throw error;
    }
});
exports.processRecipeGeneration = processRecipeGeneration;
/**
 * Find users with expiring items and queue recipe generation jobs
 */
const queueDailyRecipeGeneration = () => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        console.log('Starting daily recipe generation job queuing...');
        const today = new Date();
        today.setUTCHours(0, 0, 0, 0);
        const twoDaysFromNow = new Date(today);
        twoDaysFromNow.setUTCDate(twoDaysFromNow.getUTCDate() + 2);
        const threeDaysFromNow = new Date(today);
        threeDaysFromNow.setUTCDate(threeDaysFromNow.getUTCDate() + 3);
        // Find users with items expiring in 2-3 days
        const usersWithExpiringItems = yield schemas_1.FridgeItem.aggregate([
            {
                $match: {
                    expiryDate: {
                        $gte: twoDaysFromNow,
                        $lte: threeDaysFromNow
                    }
                }
            },
            {
                $group: {
                    _id: '$userId',
                    expiringItems: {
                        $push: {
                            itemId: '$_id',
                            name: '$name',
                            expiryDate: '$expiryDate',
                            isFavorite: '$isFavorite'
                        }
                    },
                    count: { $sum: 1 }
                }
            },
            {
                $match: {
                    count: { $gte: 2 } // Only users with at least 2 expiring items
                }
            }
        ]);
        console.log(`Found ${usersWithExpiringItems.length} users with expiring items`);
        let jobsQueued = 0;
        for (const userGroup of usersWithExpiringItems) {
            const userId = userGroup._id.toString();
            // Get user info to determine tier
            const user = yield schemas_1.Account.findById(userId).lean();
            if (!user)
                continue;
            const userTier = ((_a = user.finances) === null || _a === void 0 ? void 0 : _a.subscription_plan) === 'free' ? 'FREE' : 'PRO';
            // Check if user hasn't reached daily limit
            const usage = yield schemas_1.UserRecipeUsage.getOrCreateTodayUsage(new mongoose_1.default.Types.ObjectId(userId), userTier);
            const dailyLimit = userTier === 'PRO' ? 15 : 5;
            if (usage.generatedCount >= dailyLimit) {
                console.log(`User ${userId} has reached daily limit, skipping`);
                continue;
            }
            // Select up to 5 expiring items (prioritize favorites)
            const sortedItems = userGroup.expiringItems
                .sort((a, b) => {
                if (a.isFavorite && !b.isFavorite)
                    return -1;
                if (!a.isFavorite && b.isFavorite)
                    return 1;
                return new Date(a.expiryDate).getTime() - new Date(b.expiryDate).getTime();
            })
                .slice(0, 5);
            const expiringItemIds = sortedItems.map((item) => item.itemId.toString());
            // Queue recipe generation job
            const jobPayload = {
                userId,
                expiringItemIds,
                userTier,
                jobType: 'daily_auto'
            };
            yield queue_1.recipeGenerationQueue.add('generate-recipe', jobPayload, {
                delay: Math.random() * 30000, // Random delay up to 30 seconds to spread load
                attempts: 2,
                backoff: {
                    type: 'exponential',
                    delay: 5000
                }
            });
            jobsQueued++;
        }
        console.log(`Queued ${jobsQueued} daily recipe generation jobs`);
        return { success: true, jobsQueued };
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error('Failed to queue daily recipe generation jobs:', errorMessage);
        throw error;
    }
});
exports.queueDailyRecipeGeneration = queueDailyRecipeGeneration;
