import mongoose from 'mongoose';
import { FridgeItem } from '../database/schemas';
import { CacheService } from './CacheService';
import {
  CreateFridgeItemDto,
  UpdateFridgeItemDto,
  FridgeItemsQueryParams,
  ServiceResponse,
  RecommendationItem
} from '../types';

export class FridgeService {
  private cacheService: CacheService;

  constructor() {
    this.cacheService = new CacheService();
  }
  /**
   * Get all fridge items for a user with pagination and filtering
   */
  async getUserFridgeItems(
    userId: string, 
    queryParams: FridgeItemsQueryParams = {}
  ): Promise<ServiceResponse<{ items: any[], pagination: any }>> {
    try {
      const {
        page = 1,
        limit = 20,
        sortBy = 'expiryDate',
        sortOrder = 'asc',
        expiryStatus,
        isFavorite,
        search
      } = queryParams;

      // Validate userId format
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        return {
          success: false,
          error: 'Invalid user ID format',
          statusCode: 400
        };
      }

      // Build filter query
      const filter: any = { userId: new mongoose.Types.ObjectId(userId) };

      if (isFavorite !== undefined) {
        filter.isFavorite = isFavorite;
      }

      if (search) {
        filter.name = { $regex: search, $options: 'i' };
      }

      // Handle expiry status filtering
      if (expiryStatus) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        switch (expiryStatus) {
          case 'expired':
            filter.expiryDate = { $lt: today };
            break;
          case 'expires_today':
            // Match items that expire exactly today
            const endOfToday = new Date(today);
            endOfToday.setHours(23, 59, 59, 999);
            filter.expiryDate = { $gte: today, $lte: endOfToday };
            break;
          case 'expires_soon':
            const threeDaysFromNow = new Date(today);
            threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);
            filter.expiryDate = { $gte: today, $lte: threeDaysFromNow };
            break;
          case 'expires_this_week':
            const sevenDaysFromNow = new Date(today);
            sevenDaysFromNow.setDate(sevenDaysFromNow.getDate() + 7);
            filter.expiryDate = { $gte: today, $lte: sevenDaysFromNow };
            break;
          case 'fresh':
            const sevenDaysFromNowFresh = new Date(today);
            sevenDaysFromNowFresh.setDate(sevenDaysFromNowFresh.getDate() + 7);
            filter.expiryDate = { $gt: sevenDaysFromNowFresh };
            break;
        }
      }

      // Build sort object
      const allowedSortFields = ['expiryDate', 'name', 'createdAt', 'updatedAt'];
      const validatedSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'expiryDate';
      const sort: any = {};
      sort[validatedSortBy] = sortOrder === 'asc' ? 1 : -1;

      // Calculate pagination
      const skip = (page - 1) * limit;

      // Execute queries
      const [items, total] = await Promise.all([
        FridgeItem.find(filter)
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        FridgeItem.countDocuments(filter)
      ]);

      // Calculate pagination metadata
      const totalPages = Math.ceil(total / limit);
      const pagination = {
        page,
        limit,
        total,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      };

      return {
        success: true,
        data: { items, pagination }
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        statusCode: 500
      };
    }
  }

  /**
   * Create a new fridge item
   */
  async createFridgeItem(
    userId: string, 
    itemData: CreateFridgeItemDto
  ): Promise<ServiceResponse<any>> {
    try {
      // Validate expiry date
      const expiryDate = new Date(itemData.expiryDate);
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);

      if (expiryDate < tomorrow) {
        return {
          success: false,
          error: 'Expiry date must be at least tomorrow',
          statusCode: 400
        };
      }

      // Validate userId format
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        return {
          success: false,
          error: 'Invalid user ID format',
          statusCode: 400
        };
      }

      // Create the item
      const fridgeItem = new FridgeItem({
        userId: new mongoose.Types.ObjectId(userId),
        name: itemData.name.trim(),
        quantity: itemData.quantity,
        unit: itemData.unit,
        expiryDate,
        isFavorite: itemData.isFavorite || false
      });

      const savedItem = await fridgeItem.save();

      // Invalidate cache for user's fridge items and recommendations
      await this.cacheService.invalidateFridgeItemsCache(userId);
      await this.cacheService.delete(`recommendations:${userId}`);

      return {
        success: true,
        data: savedItem
      };
    } catch (error: any) {
      if (error.name === 'ValidationError') {
        return {
          success: false,
          error: 'Validation failed',
          statusCode: 400
        };
      }

      return {
        success: false,
        error: error.message,
        statusCode: 500
      };
    }
  }

  /**
   * Update an existing fridge item
   */
  async updateFridgeItem(
    userId: string, 
    itemId: string, 
    updateData: UpdateFridgeItemDto
  ): Promise<ServiceResponse<any>> {
    try {
      // Verify ownership and get item
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        return {
          success: false,
          error: 'Invalid user ID format',
          statusCode: 400
        };
      }
       if (!mongoose.Types.ObjectId.isValid(itemId)) {
        return {
          success: false,
          error: 'Invalid item ID format',
          statusCode: 400
        };
      }
      const existingItem = await FridgeItem.findOne({
        _id: new mongoose.Types.ObjectId(itemId),
        userId: new mongoose.Types.ObjectId(userId)
      });

      if (!existingItem) {
        return {
          success: false,
          error: 'Fridge item not found or access denied',
          statusCode: 404
        };
      }

      // Validate expiry date if provided
      if (updateData.expiryDate) {
        const expiryDate = new Date(updateData.expiryDate);
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(0, 0, 0, 0);

        if (expiryDate < tomorrow) {
          return {
            success: false,
            error: 'Expiry date must be at least tomorrow',
            statusCode: 400
          };
        }
      }

      // Prepare update object
      const updateObject: any = {};
      if (updateData.name !== undefined) updateObject.name = updateData.name.trim();
      if (updateData.quantity !== undefined) updateObject.quantity = updateData.quantity;
      if (updateData.unit !== undefined) updateObject.unit = updateData.unit;
      if (updateData.expiryDate !== undefined) updateObject.expiryDate = new Date(updateData.expiryDate);
      if (updateData.isFavorite !== undefined) updateObject.isFavorite = updateData.isFavorite;

      // Update the item
      const updatedItem = await FridgeItem.findByIdAndUpdate(
        itemId,
        updateObject,
        { new: true, runValidators: true }
      );

      // Invalidate cache for user's fridge items and recommendations
      await this.cacheService.invalidateFridgeItemsCache(userId);
      await this.cacheService.delete(`recommendations:${userId}`);

      return {
        success: true,
        data: updatedItem
      };
    } catch (error: any) {
      if (error.name === 'ValidationError') {
        return {
          success: false,
          error: 'Validation failed',
          statusCode: 400
        };
      }

      return {
        success: false,
        error: error.message,
        statusCode: 500
      };
    }
  }

  /**
   * Delete a fridge item
   */
  async deleteFridgeItem(userId: string, itemId: string): Promise<ServiceResponse<any>> {
    try {
      if (!mongoose.Types.ObjectId.isValid(userId) || !mongoose.Types.ObjectId.isValid(itemId)) {
        return {
        success: false,
        error: 'Invalid ID format',
        statusCode: 400
      };
    }

      const deletedItem = await FridgeItem.findOneAndDelete({
        _id: new mongoose.Types.ObjectId(itemId),
        userId: new mongoose.Types.ObjectId(userId)
      });

      if (!deletedItem) {
        return {
          success: false,
          error: 'Fridge item not found or access denied',
          statusCode: 404
        };
      }

      // Invalidate cache for user's fridge items and recommendations
      await this.cacheService.invalidateFridgeItemsCache(userId);
      await this.cacheService.delete(`recommendations:${userId}`);

      return {
        success: true,
        data: { message: 'Fridge item deleted successfully' }
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        statusCode: 500
      };
    }
  }

  /**
   * Toggle favorite status of a fridge item
   */
  async toggleFavoriteStatus(userId: string, itemId: string): Promise<ServiceResponse<any>> {
    try {
      if (!mongoose.Types.ObjectId.isValid(userId) || !mongoose.Types.ObjectId.isValid(itemId)) {
        return {
          success: false,
          error: 'Invalid ID format',
          statusCode: 400
        };
      }
      const item = await FridgeItem.findOne({
        _id: new mongoose.Types.ObjectId(itemId),
        userId: new mongoose.Types.ObjectId(userId)
      });

      if (!item) {
        return {
          success: false,
          error: 'Fridge item not found or access denied',
          statusCode: 404
        };
      }

      item.isFavorite = !item.isFavorite;
      const updatedItem = await item.save();

      return {
        success: true,
        data: updatedItem
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        statusCode: 500
      };
    }
  }

  /**
   * Get only favorite items for a user
   */
  async getFavoriteItems(userId: string): Promise<ServiceResponse<any[]>> {
    try {
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        return {
          success: false,
          error: 'Invalid user ID format',
          statusCode: 400
        };
      }

      const favoriteItems = await FridgeItem.find({
        userId: new mongoose.Types.ObjectId(userId),
        isFavorite: true
      })
      .sort({ expiryDate: 1 })
      .lean();

      return {
        success: true,
        data: favoriteItems
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        statusCode: 500
      };
    }
  }

  /**
   * Get recommended items based on priority algorithm
   */
  async getRecommendedItems(userId: string): Promise<ServiceResponse<RecommendationItem[]>> {
    try {
      // Check cache first
      const cachedRecommendations = await this.cacheService.getCachedRecommendations(userId);
      if (cachedRecommendations) {
        return {
          success: true,
          data: cachedRecommendations
        };
      }
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const threeDaysFromNow = new Date(today);
      threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);

      const sevenDaysFromNow = new Date(today);
      sevenDaysFromNow.setDate(sevenDaysFromNow.getDate() + 7);

      // Validate userId format
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        return {
          success: false,
          error: 'Invalid user ID format',
          statusCode: 400
        };
      }

      // Get all user items
      const allItems = await FridgeItem.find({
        userId: new mongoose.Types.ObjectId(userId)
      }).lean();

      const recommendations: RecommendationItem[] = [];

      allItems.forEach((item: any) => {
        const expiryDate = new Date(item.expiryDate);
        const daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

        // Priority 1: Items expiring within 3 days (highest priority)
        if (expiryDate <= threeDaysFromNow) {
          recommendations.push({
            ...this.formatFridgeItemResponse(item),
            priority: 'high',
            reason: daysUntilExpiry <= 0
              ? 'This item has expired and should be used immediately or discarded'
              : daysUntilExpiry === 1
                ? 'This item expires tomorrow'
                : `This item expires in ${daysUntilExpiry} days`
          });
        }
        // Priority 2: Favorite items expiring within 7 days (medium priority)
        else if (item.isFavorite && expiryDate <= sevenDaysFromNow) {
          recommendations.push({
            ...this.formatFridgeItemResponse(item),
            priority: 'medium',
            reason: `Your favorite item expires in ${daysUntilExpiry} days`
          });
        }
        // Priority 3: All other favorite items (low priority)
        else if (item.isFavorite) {
          recommendations.push({
            ...this.formatFridgeItemResponse(item),
            priority: 'low',
            reason: 'This is one of your favorite items'
          });
        }
      });

      // Sort by priority and then by expiry date
      recommendations.sort((a, b) => {
        const priorityOrder: Record<'high' | 'medium' | 'low', number> = { high: 3, medium: 2, low: 1 };
        const priorityDiff = priorityOrder[b.priority as 'high'|'medium'|'low'] - priorityOrder[a.priority as 'high'|'medium'|'low'];

        if (priorityDiff !== 0) return priorityDiff;

        // If same priority, sort by expiry date (ascending)
        return new Date(a.expiryDate).getTime() - new Date(b.expiryDate).getTime();
      });

      // Cache the recommendations for 1 hour
      await this.cacheService.cacheRecommendations(userId, recommendations, 60 * 60);

      return {
        success: true,
        data: recommendations
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        statusCode: 500
      };
    }
  }

  /**
   * Helper method to format fridge item for API response
   */
  private formatFridgeItemResponse(item: any): any {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    // Safely handle expiry date calculations
    let daysUntilExpiry = null;
    let expiryStatus = 'fresh';
    
    if (item.expiryDate) {
      const expiryDate = new Date(item.expiryDate);
      expiryDate.setHours(0, 0, 0, 0);
      
      daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
      
      if (daysUntilExpiry < 0) expiryStatus = 'expired';
      else if (daysUntilExpiry === 0) expiryStatus = 'expires_today';
      else if (daysUntilExpiry <= 3) expiryStatus = 'expires_soon';
      else if (daysUntilExpiry <= 7) expiryStatus = 'expires_this_week';
    }

    return {
      id: item._id?.toString() || null,
      name: item.name || '',
      quantity: item.quantity || 0,
      unit: item.unit || 'pieces',
      expiryDate: item.expiryDate ? item.expiryDate.toISOString() : null,
      isFavorite: item.isFavorite || false,
      createdAt: item.createdAt ? item.createdAt.toISOString() : null,
      updatedAt: item.updatedAt ? item.updatedAt.toISOString() : null,
      daysUntilExpiry,
      expiryStatus
    };
  }
}
