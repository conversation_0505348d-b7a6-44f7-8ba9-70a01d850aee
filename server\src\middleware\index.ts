import {AuthenticateToken, AuthenticateTokenOAuth, signToken, signTokenOAuth} from './authentication'
import {
  errorHandler,
  tryCatch,
  ValidationError,
  NotFoundError,
  UnauthorizedError,
  ForbiddenError,
  RateLimitError,
  ServiceUnavailableError
} from './errorHandling'
import {
  validate,
  validateCreateFridgeItem,
  validateUpdateFridgeItem,
  validateFridgeItemsQuery,
  validateGenerateRecipe,
  validateRecipesQuery,
  validateMongoId,
  validatePagination
} from './validation'
import {
  generalRateLimit,
  recipeGenerationRateLimit,
  fridgeOperationsRateLimit,
  authRateLimit,
  createCustomRateLimit
} from './rateLimiting'

export {
    // Authentication
    AuthenticateToken,
    AuthenticateTokenOAuth,
    signToken,
    signTokenOAuth,

    // Error handling
    errorHandler,
    tryCatch,
    ValidationError,
    NotFoundError,
    UnauthorizedError,
    ForbiddenError,
    RateLimitError,
    ServiceUnavailableError,

    // Validation
    validate,
    validateCreateFridgeItem,
    validateUpdateFridgeItem,
    validateFridgeItemsQuery,
    validateGenerateRecipe,
    validateRecipesQuery,
    validateMongoId,
    validatePagination,

    // Rate limiting
    generalRateLimit,
    recipeGenerationRateLimit,
    fridgeOperationsRateLimit,
    authRateLimit,
    createCustomRateLimit
}