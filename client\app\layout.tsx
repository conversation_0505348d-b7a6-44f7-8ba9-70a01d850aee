import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Leftover Chef",
  description: 'An AI Agent that tracks your fridge and pantry items, suggest recipes, prevent spoiling - less waste, more savings.',
  keywords: [
    "Recipe app",
    "Cooking app",
    "Meal planner",
    "Leftovers recipes",
    "Food waste",
    "Easy recipes",
    "Quick meals",
    "Home cooking",
    "Dinner ideas",
    "Ingredient recipes",
    "Fridge meals",
    "Budget cooking",
    "Meal prep",
    "Recipe generator",
    "Food app",
  ],
  openGraph: {
    images: [
      {
        url: "/logo/leftover-chef-leaflogo.png",
        width: 1200,
        height: 630,
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    images: "/logo/leftover-chef-leaflogo.png",
  },
  icons: {
    icon: [
      {
        url: "/logo/leftover-chef-leaflogo.png",
        type: "image/x-icon",
      },
    ],
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
