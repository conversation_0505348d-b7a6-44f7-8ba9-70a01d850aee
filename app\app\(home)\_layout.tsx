import * as SplashScreen from 'expo-splash-screen';
import 'react-native-reanimated';

import Toast from 'react-native-toast-message';
import { Drawer } from 'expo-router/drawer';
import { Ionicons } from '@expo/vector-icons';
import { StatusBarExpo } from '@/components/templates/Statusbar';
import { View, Text, StyleSheet, Image, Pressable } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { DrawerContentScrollView, DrawerItemList, DrawerItem } from '@react-navigation/drawer';
import FridgeIcon from '@/components/atoms/icons/Fridge';
import { useFonts } from 'expo-font';
import { useContext, useEffect } from 'react';
import { Dimensions } from 'react-native';
import i18n from '@/i18n/i18n';
import { AuthContext } from '@/context/AuthContext';
import { router } from 'expo-router';






// Custom Drawer Content Component
function CustomDrawerContent(props: any) {
  const { state, descriptors, navigation } = props;
  const { userData } = useContext(AuthContext)

  // Filter out the "Settings" route from the drawer items
  const filteredRoutes = state.routes.filter((route: any) => route.name !== 'settings');
  const filteredDescriptors = Object.fromEntries(
    filteredRoutes.map((route: any) => [route.key, descriptors[route.key]])
  );

  const filteredState = {
    ...state,
    routes: filteredRoutes,
  };

  return (
    <View style={styles.container}>
      {/* Scrollable Content */}
      <DrawerContentScrollView {...props} contentContainerStyle={styles.drawerContent}>
        <View style={styles.headerContainer}>
          <Image
            source={require('@/assets/pictures/logotipo-black.png')}
            style={{ width: '100%', height: 30, resizeMode: 'contain' }} />
        </View>


        {/* Filtered Drawer Items (excluding Settings) */}
        <DrawerItemList state={filteredState} descriptors={filteredDescriptors} navigation={navigation} {...props} />
      </DrawerContentScrollView>


      {/* Sticky Footer with Custom Settings */}
      <Pressable
        style={styles.stickyFooter}
        onPress={() => {
          navigation.closeDrawer(); // Close the drawer
          router.push('/settings')
        }}
      >
        <View style={styles.customItem}>
          <Image
            source={{ uri: userData?.user.profile_picture }}
            style={{ height: 40, width: 40, borderRadius: 50 }}
          />
          <Text style={styles.customItemLabel}>
            Federico
          </Text>
        </View>
        <Ionicons name='settings-outline' size={20} color={'#00000090'} />
      </Pressable>
    </View>
  );
}


export default function RootLayout() {

  // Prevent the splash screen from auto-hiding before asset loading is complete.
  SplashScreen.preventAutoHideAsync();


  const [loaded] = useFonts({
    //SpaceMono: require('../../assets/fonts/SpaceMono-Regular.ttf'),
  });

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded) {
    return null;
  }




  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <Drawer
        drawerContent={(props) => <CustomDrawerContent {...props} />}
        screenOptions={{
          headerShown: false,
          swipeEnabled: true,
          swipeEdgeWidth: Dimensions.get('window').width,
          overlayColor: 'transparent', // Remove darkening effect
          drawerType: 'slide',
          drawerStyle: {
            backgroundColor: '#FFFFFF',
            width: Dimensions.get('window').width - 100,
          },
          drawerActiveBackgroundColor: 'rgba(34,197,94,0.7)',
          drawerItemStyle: {
            borderRadius: 10
          },
          drawerActiveTintColor: 'white',
          drawerInactiveTintColor: '#4B5563',
          drawerLabelStyle: {
            fontSize: 16,
            fontWeight: '500',
          },
        }}
      >
        <Drawer.Screen
          name="index"
          options={{
            drawerLabel: i18n.t('menu.home'),
            title: 'Home',
            drawerIcon: ({ color, size }) => (
              <Ionicons name="home-outline" size={size} color={color} />
            ),
          }}
        />
        <Drawer.Screen
          name="fridge"
          options={{
            drawerLabel: i18n.t('menu.fridge'),
            title: 'Fridge',
            drawerIcon: ({ color, size }) => (
              <FridgeIcon color={color} size={size} />
            ),
          }}
        />
        <Drawer.Screen
          name="settings"
          options={{
            drawerLabel: i18n.t('menu.settings'),
            title: 'Settings',
            drawerIcon: ({ color, size }) => (
              <Ionicons name="settings-outline" size={size} color={color} />
            ),
            drawerItemStyle: {
              display: 'none'
            }
          }}
        />
      </Drawer>
      <StatusBarExpo style="dark" />
      <Toast position="bottom" bottomOffset={30} />
    </GestureHandlerRootView>

  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1, // Ensures the container takes full height
  },
  drawerContent: {
    flexGrow: 1, // Allows scrollable content to grow but not affect sticky footer
  },
  headerContainer: {
    padding: 20,
    marginBottom: 40
  },
  headerText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  customItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    paddingHorizontal: 30,
    borderRadius: 8,
    width: '85%'
  },
  customItemLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#00000090', // Red color for settings and logout
  },
  stickyFooter: {
    position: 'absolute',
    bottom: 20,
    left: 0,
    right: 0,
    backgroundColor: 'transparent', // Match drawer background or customize
    paddingBottom: 10, // Optional padding for better spacing

    flexDirection: 'row',
    alignItems: 'center',
  },
});