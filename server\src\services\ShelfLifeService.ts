import { FoodShelfLife } from '../database/schemas';
import { CacheService } from './CacheService';
import { ServiceResponse } from '../types';

export interface ShelfLifeData {
  foodName: string;
  averageShelfLifeDays: number;
  confidence: number;
  category?: string;
  storageConditions?: string;
  source: 'DATABASE' | 'AI_GENERATED' | 'USER_PROVIDED';
}

export interface AIShelfLifeResponse {
  averageShelfLifeDays: number;
  confidence: number;
  category?: string;
  storageConditions?: string;
}

export class ShelfLifeService {
  private cacheService: CacheService;
  private readonly DEFAULT_SHELF_LIFE = 7; // Default fallback in days
  private readonly AI_CACHE_TTL = 30 * 24 * 60 * 60; // 30 days in seconds
  private readonly MAX_RETRIES = 3;
  private readonly RETRY_DELAY = 1000; // 1 second

  constructor() {
    this.cacheService = new CacheService();
  }

  /**
   * Get shelf life for a food item with comprehensive fallback strategy
   */
  async getShelfLife(foodName: string, category?: string): Promise<ServiceResponse<ShelfLifeData>> {
    try {
      // Step 1: Check database first
      const dbResult = await this.getShelfLifeFromDatabase(foodName);
      if (dbResult.success && dbResult.data) {
        return dbResult;
      }

      // Step 2: Check AI cache
      const cacheKey = `shelf_life_ai:${foodName.toLowerCase().trim()}`;
      const cachedData = await this.cacheService.get(cacheKey);
      if (cachedData) {
        console.log(`Shelf life found in AI cache for: ${foodName}`);
        return {
          success: true,
          data: {
            foodName,
            ...cachedData,
            source: 'AI_GENERATED' as const
          }
        };
      }

      // Step 3: Try AI service with retries
      const aiResult = await this.getShelfLifeFromAIWithRetry(foodName, category);
      if (aiResult.success && aiResult.data) {
        // Cache the AI result
        await this.cacheService.set(cacheKey, aiResult.data, this.AI_CACHE_TTL);
        
        // Store in database for future use
        await this.storeShelfLifeData({
          foodName,
          ...aiResult.data,
          source: 'AI_GENERATED'
        });

        return {
          success: true,
          data: {
            foodName,
            ...aiResult.data,
            source: 'AI_GENERATED' as const
          }
        };
      }

      // Step 4: Fallback to default
      console.warn(`Using default shelf life for unknown food: ${foodName}`);
      const fallbackData: ShelfLifeData = {
        foodName,
        averageShelfLifeDays: this.DEFAULT_SHELF_LIFE,
        confidence: 0.3, // Low confidence for fallback
        category: category || 'other',
        source: 'DATABASE',
        storageConditions: 'Store in refrigerator'
      };

      return {
        success: true,
        data: fallbackData
      };

    } catch (error: any) {
      console.error(`Error getting shelf life for ${foodName}:`, error.message);
      
      // Return fallback data even on error
      return {
        success: true,
        data: {
          foodName,
          averageShelfLifeDays: this.DEFAULT_SHELF_LIFE,
          confidence: 0.2,
          category: category || 'other',
          source: 'DATABASE' as const,
          storageConditions: 'Store in refrigerator'
        }
      };
    }
  }

  /**
   * Get shelf life from database with fuzzy matching
   */
  async getShelfLifeFromDatabase(foodName: string): Promise<ServiceResponse<ShelfLifeData>> {
    try {
      const result = await FoodShelfLife.findByFoodName(foodName);
      
      if (result) {
        return {
          success: true,
          data: {
            foodName: result.foodName,
            averageShelfLifeDays: result.averageShelfLifeDays,
            confidence: result.confidence,
            category: result.category,
            storageConditions: result.storageConditions,
            source: result.source
          }
        };
      }

      return {
        success: false,
        error: 'Food not found in database'
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        statusCode: 500
      };
    }
  }

  /**
   * Get similar foods from database
   */
  async getSimilarFoods(foodName: string, limit: number = 5): Promise<ServiceResponse<ShelfLifeData[]>> {
    try {
      const results = await FoodShelfLife.findSimilarFoods(foodName, limit);
      
      const similarFoods = results.map(item => ({
        foodName: item.foodName,
        averageShelfLifeDays: item.averageShelfLifeDays,
        confidence: item.confidence,
        category: item.category,
        storageConditions: item.storageConditions,
        source: item.source
      }));

      return {
        success: true,
        data: similarFoods
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        statusCode: 500
      };
    }
  }

  /**
   * Store shelf life data in database
   */
  async storeShelfLifeData(data: ShelfLifeData): Promise<ServiceResponse<any>> {
    try {
      const result = await FoodShelfLife.createOrUpdate(data);
      
      return {
        success: true,
        data: result
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        statusCode: 500
      };
    }
  }

  /**
   * Get shelf life from AI service with retry logic
   */
  private async getShelfLifeFromAIWithRetry(
    foodName: string, 
    category?: string
  ): Promise<ServiceResponse<AIShelfLifeResponse>> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.MAX_RETRIES; attempt++) {
      try {
        console.log(`AI shelf life lookup attempt ${attempt} for: ${foodName}`);
        
        const result = await this.callAIService(foodName, category);
        if (result.success) {
          return result;
        }
        
        lastError = new Error(result.error || 'AI service returned unsuccessful response');
        
      } catch (error: any) {
        lastError = error;
        console.warn(`AI service attempt ${attempt} failed:`, error.message);
      }

      // Wait before retry (exponential backoff)
      if (attempt < this.MAX_RETRIES) {
        await this.delay(this.RETRY_DELAY * Math.pow(2, attempt - 1));
      }
    }

    return {
      success: false,
      error: `AI service failed after ${this.MAX_RETRIES} attempts: ${lastError?.message}`,
      statusCode: 503
    };
  }

  /**
   * Call AI service to get shelf life information
   */
  private async callAIService(
    foodName: string, 
    category?: string
  ): Promise<ServiceResponse<AIShelfLifeResponse>> {
    try {
      // This is a placeholder implementation
      // In production, replace with actual AI service call (OpenAI, Claude, etc.)
      
      console.log(`Calling AI service for shelf life of: ${foodName}${category ? ` (category: ${category})` : ''}`);

      // Simulate AI processing time
      await this.delay(500 + Math.random() * 1000);

      // Mock AI response based on common food categories
      const mockResponse = this.generateMockAIResponse(foodName, category);

      // Simulate occasional failures for testing
      if (Math.random() < 0.05) { // 5% failure rate
        throw new Error('Simulated AI service timeout');
      }

      return {
        success: true,
        data: mockResponse
      };

      /* 
      // Real implementation would look like this:
      
      const prompt = `Provide shelf life information for "${foodName}"${category ? ` in category "${category}"` : ''}. 
      Return JSON with: averageShelfLifeDays (number), confidence (0-1), category (string), storageConditions (string).
      Consider typical refrigerated storage conditions.`;

      const response = await fetch(process.env.AI_SERVICE_URL!, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.AI_SERVICE_API_KEY}`
        },
        body: JSON.stringify({
          model: 'gpt-3.5-turbo',
          messages: [{ role: 'user', content: prompt }],
          temperature: 0.3
        })
      });

      if (!response.ok) {
        throw new Error(`AI service responded with status: ${response.status}`);
      }

      const result = await response.json();
      const aiData = JSON.parse(result.choices[0].message.content);

      return {
        success: true,
        data: {
          averageShelfLifeDays: Math.max(1, Math.min(365, aiData.averageShelfLifeDays)),
          confidence: Math.max(0, Math.min(1, aiData.confidence)),
          category: aiData.category,
          storageConditions: aiData.storageConditions
        }
      };
      */

    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        statusCode: 503
      };
    }
  }

  /**
   * Generate mock AI response for testing
   */
  private generateMockAIResponse(foodName: string, category?: string): AIShelfLifeResponse {
    const lowerName = foodName.toLowerCase();
    
    // Mock responses based on common foods
    const mockData: Record<string, Partial<AIShelfLifeResponse>> = {
      'tomato': { averageShelfLifeDays: 7, confidence: 0.9, category: 'vegetables' },
      'apple': { averageShelfLifeDays: 14, confidence: 0.95, category: 'fruits' },
      'banana': { averageShelfLifeDays: 5, confidence: 0.9, category: 'fruits' },
      'milk': { averageShelfLifeDays: 7, confidence: 0.95, category: 'dairy' },
      'bread': { averageShelfLifeDays: 5, confidence: 0.9, category: 'baked_goods' },
      'chicken': { averageShelfLifeDays: 3, confidence: 0.95, category: 'poultry' },
      'lettuce': { averageShelfLifeDays: 7, confidence: 0.85, category: 'vegetables' },
      'cheese': { averageShelfLifeDays: 21, confidence: 0.9, category: 'dairy' }
    };

    const baseData = mockData[lowerName] || {
      averageShelfLifeDays: this.DEFAULT_SHELF_LIFE,
      confidence: 0.7,
      category: category || 'other'
    };

    return {
      averageShelfLifeDays: baseData.averageShelfLifeDays!,
      confidence: baseData.confidence!,
      category: baseData.category,
      storageConditions: `Store in refrigerator at 4°C or below`
    };
  }

  /**
   * Utility method for delays
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Batch process multiple food items
   */
  async batchGetShelfLife(foodNames: string[]): Promise<ServiceResponse<Record<string, ShelfLifeData>>> {
    try {
      const results: Record<string, ShelfLifeData> = {};
      
      // Process in parallel but limit concurrency
      const batchSize = 5;
      for (let i = 0; i < foodNames.length; i += batchSize) {
        const batch = foodNames.slice(i, i + batchSize);
        const promises = batch.map(async (foodName) => {
          const result = await this.getShelfLife(foodName);
          return { foodName, result };
        });

        const batchResults = await Promise.all(promises);
        batchResults.forEach(({ foodName, result }) => {
          if (result.success && result.data) {
            results[foodName] = result.data;
          }
        });
      }

      return {
        success: true,
        data: results
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        statusCode: 500
      };
    }
  }
}
