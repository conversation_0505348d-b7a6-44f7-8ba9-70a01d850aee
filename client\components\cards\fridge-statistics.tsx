"use client"

import { Clock, ShoppingBasket, AlertTriangle } from "lucide-react"

export default function FridgeStatistics() {
    // Sample data - replace with your actual data
    const stats = {
        expiringToday: 3,
        expiringWithinThreeDays: 7,
        totalItems: 24,
    }

    return (
        <div className="w-full max-w-4xl mx-auto p-4">
            <h2 className="text-2xl font-bold mb-6 text-gray-400">Fridge Statistics</h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Expiring Today Card */}
                <div className="bg-white rounded-xl shadow-md overflow-hidden border border-red-100">
                    <div className="p-3">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center">
                                <div className="bg-red-100 p-3 rounded-full">
                                    <AlertTriangle className="h-6 w-6 text-red-500" />
                                </div>
                                <h3 className="ml-4 text-lg font-medium text-gray-400">Expiring Today</h3>
                            </div>
                        </div>
                        <div className="mt-6">
                            <p className="text-4xl font-bold text-red-500">{stats.expiringToday}</p>
                            <p className="mt-1 text-sm text-gray-500">items need attention</p>
                        </div>
                    </div>
                </div>

                {/* Expiring Within 3 Days Card */}
                <div className="bg-white rounded-xl shadow-md overflow-hidden border border-amber-100">
                    <div className="p-3">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center">
                                <div className="bg-amber-100 p-3 rounded-full">
                                    <Clock className="h-6 w-6 text-amber-500" />
                                </div>
                                <h3 className="ml-4 text-lg font-medium text-gray-400">Expiring Soon</h3>
                            </div>
                        </div>
                        <div className="mt-6">
                            <p className="text-4xl font-bold text-amber-500">{stats.expiringWithinThreeDays}</p>
                            <p className="mt-1 text-sm text-gray-500">items expiring within 3 days</p>
                        </div>
                    </div>
                </div>

                {/* Total Items Card */}
                <div className="bg-white rounded-xl shadow-md overflow-hidden border border-green-100">
                    <div className="p-3">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center">
                                <div className="bg-green-100 p-3 rounded-full">
                                    <ShoppingBasket className="h-6 w-6 text-green-500" />
                                </div>
                                <h3 className="ml-4 text-lg font-medium text-gray-400">Current Items</h3>
                            </div>
                        </div>
                        <div className="mt-6">
                            <p className="text-4xl font-bold text-green-500">{stats.totalItems}</p>
                            <p className="mt-1 text-sm text-gray-500">items in your fridge</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}
