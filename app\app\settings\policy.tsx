import React, { useState } from 'react'
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import { Ionicons } from "@expo/vector-icons"

import Page from '@/components/templates/Page'
import { ScrollView } from 'react-native-gesture-handler'
import Header from '@/components/templates/Header'

interface Section {
  id: string
  title: string
  content: string[]
  subsections?: { title: string; content: string[] }[]
}
const privacyData: Section[] = [
  {
    id: "overview",
    title: "1. Privacy Overview",
    content: [
      "At Hazel, we are committed to protecting your privacy and ensuring the security of your personal information.",
      "This Privacy Policy explains how we collect, use, share, and protect your information when you use our food management application.",
      "By using Hazel, you consent to the data practices described in this policy.",
      "We will never sell your personal information to third parties for marketing purposes.",
    ],
  },
  {
    id: "information-collected",
    title: "2. Information We Collect",
    content: ["We collect several types of information to provide and improve our services:"],
    subsections: [
      {
        title: "Personal Information",
        content: [
          "Account details: Name, email address, profile picture",
          "Contact information for customer support",
          "Payment information (processed securely by third-party providers)",
          "Family member information when using family sharing features",
        ],
      },
      {
        title: "Food and Usage Data",
        content: [
          "Food items you add to your inventory",
          "Expiration dates and storage locations",
          "Photos of food items and receipts",
          "Custom recipes and meal plans",
          "Shopping lists and purchase history",
          "Dietary preferences and restrictions",
        ],
      },
      {
        title: "Device and Technical Information",
        content: [
          "Device type, operating system, and app version",
          "IP address and general location (city/region level)",
          "App usage patterns and feature interactions",
          "Crash reports and performance data",
          "Camera access for barcode scanning and photo capture",
        ],
      },
      {
        title: "Analytics and Performance Data",
        content: [
          "How you navigate and use the app",
          "Feature usage statistics",
          "Error logs and diagnostic information",
          "Time spent in different sections of the app",
        ],
      },
    ],
  },
  {
    id: "how-we-use",
    title: "3. How We Use Your Information",
    content: ["We use your information for the following purposes:"],
    subsections: [
      {
        title: "Core App Functionality",
        content: [
          "Manage your food inventory and track expiration dates",
          "Send notifications about expiring food items",
          "Provide personalized recipe recommendations",
          "Enable barcode scanning for easy item entry",
          "Sync data across your devices",
        ],
      },
      {
        title: "Service Improvement",
        content: [
          "Analyze usage patterns to improve app features",
          "Develop new functionality based on user needs",
          "Fix bugs and optimize app performance",
          "Conduct research to reduce food waste",
        ],
      },
      {
        title: "Communication",
        content: [
          "Send important updates about your account",
          "Provide customer support and respond to inquiries",
          "Share tips for reducing food waste",
          "Notify you about new features and improvements",
        ],
      },
      {
        title: "Legal and Security",
        content: [
          "Comply with legal obligations and regulations",
          "Protect against fraud and unauthorized access",
          "Enforce our Terms of Service",
          "Resolve disputes and legal claims",
        ],
      },
    ],
  },
  {
    id: "information-sharing",
    title: "4. Information Sharing and Disclosure",
    content: [
      "We do not sell, trade, or rent your personal information to third parties. We may share your information in the following limited circumstances:",
    ],
    subsections: [
      {
        title: "Service Providers",
        content: [
          "Cloud storage providers for data backup and sync",
          "Analytics services to understand app usage",
          "Payment processors for subscription billing",
          "Customer support platforms",
          "All service providers are bound by strict confidentiality agreements",
        ],
      },
      {
        title: "Family Sharing",
        content: [
          "When you use family features, certain information is shared with family members",
          "This includes shared shopping lists, meal plans, and food inventory",
          "You control what information is shared through privacy settings",
        ],
      },
      {
        title: "Legal Requirements",
        content: [
          "When required by law or legal process",
          "To protect our rights, property, or safety",
          "To protect the rights, property, or safety of our users",
          "In connection with a merger, acquisition, or sale of assets",
        ],
      },
      {
        title: "Aggregated Data",
        content: [
          "We may share anonymized, aggregated data for research purposes",
          "This data cannot be used to identify individual users",
          "Used to study food waste patterns and improve sustainability",
        ],
      },
    ],
  },
  {
    id: "data-security",
    title: "5. Data Security and Storage",
    content: ["We implement industry-standard security measures to protect your information:"],
    subsections: [
      {
        title: "Technical Safeguards",
        content: [
          "Data encryption in transit and at rest",
          "Secure cloud infrastructure with regular security audits",
          "Multi-factor authentication for admin access",
          "Regular security updates and patches",
        ],
      },
      {
        title: "Access Controls",
        content: [
          "Limited employee access to personal data",
          "Role-based permissions and audit logs",
          "Background checks for employees with data access",
          "Regular security training for all staff",
        ],
      },
      {
        title: "Data Retention",
        content: [
          "Account data is retained while your account is active",
          "Food inventory data is kept for up to 2 years for analytics",
          "Deleted data is permanently removed within 30 days",
          "Backup data is retained for disaster recovery purposes",
        ],
      },
    ],
  },
  {
    id: "your-rights",
    title: "6. Your Privacy Rights",
    content: ["You have several rights regarding your personal information:"],
    subsections: [
      {
        title: "Access and Portability",
        content: [
          "Request a copy of all personal data we have about you",
          "Export your food inventory and recipe data",
          "Receive data in a machine-readable format",
          "Transfer your data to another service",
        ],
      },
      {
        title: "Correction and Updates",
        content: [
          "Update your profile information at any time",
          "Correct inaccurate or incomplete data",
          "Modify your privacy preferences",
          "Change notification settings",
        ],
      },
      {
        title: "Deletion and Restriction",
        content: [
          "Delete your account and all associated data",
          "Request deletion of specific data categories",
          "Restrict processing of your personal information",
          "Object to certain uses of your data",
        ],
      },
      {
        title: "Communication Preferences",
        content: [
          "Opt out of marketing communications",
          "Control push notification settings",
          "Manage email preferences",
          "Unsubscribe from newsletters",
        ],
      },
    ],
  },
  {
    id: "cookies-tracking",
    title: "7. Cookies and Tracking Technologies",
    content: ["We use various technologies to collect and store information:"],
    subsections: [
      {
        title: "Essential Cookies",
        content: [
          "Required for basic app functionality",
          "Remember your login status",
          "Store your preferences and settings",
          "Cannot be disabled without affecting app performance",
        ],
      },
      {
        title: "Analytics Cookies",
        content: [
          "Help us understand how you use the app",
          "Identify popular features and usage patterns",
          "Measure app performance and errors",
          "Can be disabled in privacy settings",
        ],
      },
      {
        title: "Third-Party Services",
        content: [
          "Google Analytics for usage statistics",
          "Crash reporting services for stability",
          "Performance monitoring tools",
          "Each service has its own privacy policy",
        ],
      },
    ],
  },
  {
    id: "childrens-privacy",
    title: "8. Children's Privacy",
    content: [
      "We are committed to protecting children's privacy:",
      "Our app is not intended for children under 13 years of age",
      "We do not knowingly collect personal information from children under 13",
      "If we discover we have collected information from a child under 13, we will delete it immediately",
      "Parents can contact us to review, delete, or stop further collection of their child's information",
      "Teen users (13-17) must have parental consent to use the app",
    ],
  },
  {
    id: "international-transfers",
    title: "9. International Data Transfers",
    content: [
      "Your information may be transferred to and processed in countries other than your own:",
      "We use cloud services that may store data in multiple regions",
      "All transfers comply with applicable data protection laws",
      "We implement appropriate safeguards for international transfers",
      "EU users: We comply with GDPR requirements for data transfers",
      "We use Standard Contractual Clauses where required",
    ],
  },
  {
    id: "regional-rights",
    title: "10. Regional Privacy Rights",
    content: ["Additional rights may apply based on your location:"],
    subsections: [
      {
        title: "European Union (GDPR)",
        content: [
          "Right to data portability and erasure",
          "Right to restrict or object to processing",
          "Right to lodge complaints with supervisory authorities",
          "Legal basis for processing: legitimate interest and consent",
        ],
      },
      {
        title: "California (CCPA)",
        content: [
          "Right to know what personal information is collected",
          "Right to delete personal information",
          "Right to opt-out of sale (we don't sell data)",
          "Right to non-discrimination for exercising privacy rights",
        ],
      },
      {
        title: "Other Jurisdictions",
        content: [
          "We comply with local privacy laws where applicable",
          "Additional rights may be available in your region",
          "Contact us for information about your specific rights",
        ],
      },
    ],
  },
  {
    id: "updates",
    title: "11. Policy Updates",
    content: [
      "We may update this Privacy Policy from time to time:",
      "Material changes will be communicated through the app or email",
      "The 'Last Updated' date will reflect the most recent changes",
      "Continued use of the app after changes constitutes acceptance",
      "You can review previous versions of the policy upon request",
      "We encourage you to review this policy periodically",
    ],
  },
  {
    id: "contact",
    title: "12. Contact Information",
    content: [
      "If you have questions about this Privacy Policy or our data practices:",
      "Email: <EMAIL>",
      //"Address: [Your Company Address]",
      //"Phone: [Your Phone Number]",
      "We will respond to privacy inquiries within 30 days",
    ],
  },
]


const Index = () => {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set())

  const toggleSection = (sectionId: string) => {
    const newExpanded = new Set(expandedSections)
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId)
    } else {
      newExpanded.add(sectionId)
    }
    setExpandedSections(newExpanded)
  }

  const renderSection = (section: Section) => {
    const isExpanded = expandedSections.has(section.id)

    return (
      <View key={section.id} style={styles.sectionCard}>
        <TouchableOpacity style={styles.sectionHeader} onPress={() => toggleSection(section.id)}>
          <Text style={styles.sectionTitle}>{section.title}</Text>
          <Ionicons
            name={isExpanded ? "chevron-up" : "chevron-down"}
            size={20}
            color="#666"
            style={styles.chevronIcon}
          />
        </TouchableOpacity>

        {isExpanded && (
          <View style={styles.sectionContent}>
            {section.content.map((paragraph, index) => (
              <Text key={index} style={styles.paragraphText}>
                {paragraph}
              </Text>
            ))}

            {section.subsections?.map((subsection, subIndex) => (
              <View key={subIndex} style={styles.subsection}>
                <Text style={styles.subsectionTitle}>{subsection.title}</Text>
                {subsection.content.map((subParagraph, subParaIndex) => (
                  <View key={subParaIndex} style={styles.bulletPoint}>
                    <Text style={styles.bulletText}>•</Text>
                    <Text style={styles.bulletContent}>{subParagraph}</Text>
                  </View>
                ))}
              </View>
            ))}
          </View>
        )}
      </View>
    )
  }



  return (
    <Page noPaddingTop alignItems='center' justifyContent='space-between' >
      <Header buttonBack text=' ' />

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Introduction */}
        <View style={styles.introSection}>
          <View style={styles.privacyIcon}>
            <Ionicons name="shield-checkmark" size={32} color="rgba(34,197,94,0.7)" />
          </View>
          <Text style={styles.introTitle}>Privacy Policy</Text>
          <Text style={styles.introSubtitle}>Last updated: May 2025</Text>
          <Text style={styles.introText}>
            Your privacy matters to us. This policy explains how we collect, use, and protect your information when you
            use Hazel to manage your food inventory and reduce waste.
          </Text>
        </View> 

        {/* Privacy Highlights */}
        <View style={styles.highlightsSection}>
          <Text style={styles.highlightsTitle}>Privacy Highlights</Text>
          <View style={styles.highlightItem}>
            <Ionicons name="lock-closed" size={20} color="rgba(34,197,94,0.7)" />
            <Text style={styles.highlightText}>We never sell your personal data</Text>
          </View>
          <View style={styles.highlightItem}>
            <Ionicons name="eye-off" size={20} color="rgba(34,197,94,0.7)" />
            <Text style={styles.highlightText}>You control what data is shared</Text>
          </View>
          <View style={styles.highlightItem}>
            <Ionicons name="trash" size={20} color="rgba(34,197,94,0.7)" />
            <Text style={styles.highlightText}>Delete your data anytime</Text>
          </View>
          <View style={styles.highlightItem}>
            <Ionicons name="shield" size={20} color="rgba(34,197,94,0.7)" />
            <Text style={styles.highlightText}>Industry-standard security</Text>
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => {
              const allSectionIds = new Set(privacyData.map((section) => section.id))
              setExpandedSections(allSectionIds)
            }}
          >
            <Ionicons name="expand" size={16} color="#rgba(34,197,94,0.7)" />
            <Text style={styles.actionButtonText}>Expand All</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionButton} onPress={() => setExpandedSections(new Set())}>
            <Ionicons name="contract" size={16} color="#rgba(34,197,94,0.7)" />
            <Text style={styles.actionButtonText}>Collapse All</Text>
          </TouchableOpacity>
        </View>

        {/* Privacy Sections */}
        <View style={styles.sectionsContainer}>{privacyData.map(renderSection)}</View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            Questions about your privacy? We're here to help. Contact our privacy team for any concerns or requests.
          </Text>
          <TouchableOpacity style={styles.contactButton}>
            <Ionicons name="mail" size={16} color="rgba(34,197,94,0.7)" />
            <Text style={styles.contactButtonText}>Contact Privacy Team</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

    </Page>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e9ecef",
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: "600",
    textAlign: "center",
    color: "#333",
  },
  headerSpacer: {
    width: 32,
  },
  scrollView: {
    flex: 1,
  },
  introSection: {
    padding: 24,
    backgroundColor: "#fff",
    marginBottom: 16,
    alignItems: "center",
    marginTop: 100
  },
  privacyIcon: {
    marginBottom: 16,
  },
  introTitle: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 8,
  },
  introSubtitle: {
    fontSize: 14,
    color: "rgba(34,197,94,0.7)A",
    fontWeight: "600",
    marginBottom: 16,
  },
  introText: {
    fontSize: 16,
    color: "#666",
    lineHeight: 24,
    textAlign: "center",
    marginTop: 50
  },
  highlightsSection: {
    backgroundColor: "#fff",
    marginHorizontal: 20,
    marginBottom: 16,
    padding: 20,
    borderRadius: 12,
  },
  highlightsTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    marginBottom: 16,
  },
  highlightItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
    gap: 12,
  },
  highlightText: {
    fontSize: 14,
    color: "#555",
    flex: 1,
  },
  quickActions: {
    flexDirection: "row",
    paddingHorizontal: 20,
    marginBottom: 16,
    gap: 12,
  },
  actionButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#fff",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: "rgba(34,197,94,0.7)",
    gap: 6,
  },
  actionButtonText: {
    fontSize: 14,
    color: "rgba(34,197,94,0.7)",
    fontWeight: "500",
  },
  sectionsContainer: {
    paddingHorizontal: 20,
    gap: 12,
  },
  sectionCard: {
    backgroundColor: "#fff",
    borderRadius: 12,
    overflow: "hidden",
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    flex: 1,
    marginRight: 12,
  },
  chevronIcon: {
    marginLeft: 8,
  },
  sectionContent: {
    paddingHorizontal: 20,
    paddingTop: 10,
    borderTopWidth: 1,
    borderTopColor: "#f1f3f4",
  },
  paragraphText: {
    fontSize: 14,
    color: "#555",
    lineHeight: 20,
    marginBottom: 12,
  },
  subsection: {
    marginTop: 16,
    marginBottom: 8,
  },
  subsectionTitle: {
    fontSize: 15,
    fontWeight: "600",
    color: "#333",
    marginBottom: 8,
  },
  bulletPoint: {
    flexDirection: "row",
    marginBottom: 6,
    paddingLeft: 8,
  },
  bulletText: {
    fontSize: 14,
    color: "rgba(34,197,94,0.7)",
    marginRight: 8,
    fontWeight: "bold",
  },
  bulletContent: {
    fontSize: 14,
    color: "#555",
    lineHeight: 18,
    flex: 1,
  },
  footer: {
    padding: 24,
    backgroundColor: "#fff",
    marginTop: 16,
    marginBottom: 32,
  },
  footerText: {
    fontSize: 14,
    color: "#666",
    lineHeight: 20,
    marginBottom: 16,
    textAlign: "center",
  },
  contactButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#00000010",
    paddingVertical: 20,
    paddingHorizontal: 20,
    borderRadius: 8,
    gap: 8,
    marginBottom: 12,
  },
  contactButtonText: {
    fontSize: 14,
    color: "rgba(34,197,94,0.7)",
    fontWeight: "500",
  },
  settingsButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "rgba(34,197,94,0.7)",
    paddingVertical: 20,
    paddingHorizontal: 20,
    borderRadius: 8,
    gap: 8,
  },
  settingsButtonText: {
    fontSize: 14,
    color: "#fff",
    fontWeight: "500",
  },
})



export default Index

