"use client"

import { useEffect, useState } from "react"


interface CustomSkeletonProps {
  baseColor?: string
  highlightColor?: string
  speed?: "slow" | "normal" | "fast"
}

export function RecipeSkeletonLoader({
  baseColor = "#e2e8f0", // Default to slate-200
  highlightColor = "#00c95120", // Default to slate-50
  speed = "normal",
}: CustomSkeletonProps) {
  // Convert speed to animation duration
  const animationDuration = {
    slow: "1.8s",
    normal: "1.2s",
    fast: "0.8s",
  }[speed]

  // Create a CSS variable for the animation
  const skeletonStyle = {
    "--base-color": baseColor,
    "--highlight-color": highlightColor,
    "--animation-duration": animationDuration,
  } as React.CSSProperties

  return (
    <div className="rounded-lg border border-gray-200 overflow-hidden w-full shadow-md" style={skeletonStyle}>
      <div className="p-6 space-y-6">
        {/* Title */}
        <div className="space-y-2">
          <div 
            className="h-8 w-3/4 rounded bg-gradient-to-r from-[var(--base-color)] via-[var(--highlight-color)] to-[var(--base-color)] bg-[length:200%_100%] animate-shimmer" 
            style={{ animationDuration }}
          />
          <div 
            className="h-4 w-1/3 rounded bg-gradient-to-r from-[var(--base-color)] via-[var(--highlight-color)] to-[var(--base-color)] bg-[length:200%_100%] animate-shimmer" 
            style={{ animationDuration }}
          />
        </div>

        {/* Description */}
        <div className="space-y-2">
          <div 
            className="h-4 w-full rounded bg-gradient-to-r from-[var(--base-color)] via-[var(--highlight-color)] to-[var(--base-color)] bg-[length:200%_100%] animate-shimmer" 
            style={{ animationDuration }}
          />
          <div 
            className="h-4 w-full rounded bg-gradient-to-r from-[var(--base-color)] via-[var(--highlight-color)] to-[var(--base-color)] bg-[length:200%_100%] animate-shimmer" 
            style={{ animationDuration }}
          />
          <div 
            className="h-4 w-2/3 rounded bg-gradient-to-r from-[var(--base-color)] via-[var(--highlight-color)] to-[var(--base-color)] bg-[length:200%_100%] animate-shimmer" 
            style={{ animationDuration }}
          />
        </div>

        {/* Ingredients */}
        <div className="space-y-2">
          <div 
            className="h-6 w-1/3 rounded bg-gradient-to-r from-[var(--base-color)] via-[var(--highlight-color)] to-[var(--base-color)] bg-[length:200%_100%] animate-shimmer" 
            style={{ animationDuration }}
          />
          <div className="space-y-1 pl-5">
            {[...Array(4)].map((_, i) => (
              <div 
                key={`ingredient-${i}`}
                className="h-4 w-full rounded bg-gradient-to-r from-[var(--base-color)] via-[var(--highlight-color)] to-[var(--base-color)] bg-[length:200%_100%] animate-shimmer" 
                style={{ 
                  animationDuration,
                  animationDelay: `${i * 0.1}s`,
                  width: `${100 - (i * 5)}%`
                }}
              />
            ))}
          </div>
        </div>

        {/* Instructions */}
        <div className="space-y-2">
          <div 
            className="h-6 w-1/3 rounded bg-gradient-to-r from-[var(--base-color)] via-[var(--highlight-color)] to-[var(--base-color)] bg-[length:200%_100%] animate-shimmer" 
            style={{ animationDuration }}
          />
          <div className="space-y-3 pl-5">
            {[...Array(5)].map((_, i) => (
              <div 
                key={`step-${i}`}
                className="h-4 w-full rounded bg-gradient-to-r from-[var(--base-color)] via-[var(--highlight-color)] to-[var(--base-color)] bg-[length:200%_100%] animate-shimmer" 
                style={{ 
                  animationDuration,
                  animationDelay: `${i * 0.15}s`,
                  width: `${100 - (i * 3)}%`
                }}
              />
            ))}
          </div>
        </div>

        {/* Instructions */}
        <div className="space-y-2">
          <div 
            className="h-6 w-1/3 rounded bg-gradient-to-r from-[var(--base-color)] via-[var(--highlight-color)] to-[var(--base-color)] bg-[length:200%_100%] animate-shimmer" 
            style={{ animationDuration }}
          />
          <div className="space-y-3 pl-5">
            {[...Array(5)].map((_, i) => (
              <div 
                key={`step-${i}`}
                className="h-4 w-full rounded bg-gradient-to-r from-[var(--base-color)] via-[var(--highlight-color)] to-[var(--base-color)] bg-[length:200%_100%] animate-shimmer" 
                style={{ 
                  animationDuration,
                  animationDelay: `${i * 0.15}s`,
                  width: `${100 - (i * 3)}%`
                }}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
