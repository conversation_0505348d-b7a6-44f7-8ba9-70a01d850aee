"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FridgeService = void 0;
const tslib_1 = require("tslib");
const mongoose_1 = tslib_1.__importDefault(require("mongoose"));
const schemas_1 = require("../database/schemas");
const CacheService_1 = require("./CacheService");
class FridgeService {
    constructor() {
        this.cacheService = new CacheService_1.CacheService();
    }
    /**
     * Get all fridge items for a user with pagination and filtering
     */
    getUserFridgeItems(userId_1) {
        return tslib_1.__awaiter(this, arguments, void 0, function* (userId, queryParams = {}) {
            try {
                const { page = 1, limit = 20, sortBy = 'expiryDate', sortOrder = 'asc', expiryStatus, isFavorite, search } = queryParams;
                // Validate userId format
                if (!mongoose_1.default.Types.ObjectId.isValid(userId)) {
                    return {
                        success: false,
                        error: 'Invalid user ID format',
                        statusCode: 400
                    };
                }
                // Build filter query
                const filter = { userId: new mongoose_1.default.Types.ObjectId(userId) };
                if (isFavorite !== undefined) {
                    filter.isFavorite = isFavorite;
                }
                if (search) {
                    filter.name = { $regex: search, $options: 'i' };
                }
                // Handle expiry status filtering
                if (expiryStatus) {
                    const today = new Date();
                    today.setHours(0, 0, 0, 0);
                    switch (expiryStatus) {
                        case 'expired':
                            filter.expiryDate = { $lt: today };
                            break;
                        case 'expires_today':
                            // Match items that expire exactly today
                            const endOfToday = new Date(today);
                            endOfToday.setHours(23, 59, 59, 999);
                            filter.expiryDate = { $gte: today, $lte: endOfToday };
                            break;
                        case 'expires_soon':
                            const threeDaysFromNow = new Date(today);
                            threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);
                            filter.expiryDate = { $gte: today, $lte: threeDaysFromNow };
                            break;
                        case 'expires_this_week':
                            const sevenDaysFromNow = new Date(today);
                            sevenDaysFromNow.setDate(sevenDaysFromNow.getDate() + 7);
                            filter.expiryDate = { $gte: today, $lte: sevenDaysFromNow };
                            break;
                        case 'fresh':
                            const sevenDaysFromNowFresh = new Date(today);
                            sevenDaysFromNowFresh.setDate(sevenDaysFromNowFresh.getDate() + 7);
                            filter.expiryDate = { $gt: sevenDaysFromNowFresh };
                            break;
                    }
                }
                // Build sort object
                const allowedSortFields = ['expiryDate', 'name', 'createdAt', 'updatedAt'];
                const validatedSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'expiryDate';
                const sort = {};
                sort[validatedSortBy] = sortOrder === 'asc' ? 1 : -1;
                // Calculate pagination
                const skip = (page - 1) * limit;
                // Execute queries
                const [items, total] = yield Promise.all([
                    schemas_1.FridgeItem.find(filter)
                        .sort(sort)
                        .skip(skip)
                        .limit(limit)
                        .lean(),
                    schemas_1.FridgeItem.countDocuments(filter)
                ]);
                // Calculate pagination metadata
                const totalPages = Math.ceil(total / limit);
                const pagination = {
                    page,
                    limit,
                    total,
                    totalPages,
                    hasNextPage: page < totalPages,
                    hasPrevPage: page > 1
                };
                return {
                    success: true,
                    data: { items, pagination }
                };
            }
            catch (error) {
                return {
                    success: false,
                    error: error.message,
                    statusCode: 500
                };
            }
        });
    }
    /**
     * Create a new fridge item
     */
    createFridgeItem(userId, itemData) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                // Validate expiry date
                const expiryDate = new Date(itemData.expiryDate);
                const tomorrow = new Date();
                tomorrow.setDate(tomorrow.getDate() + 1);
                tomorrow.setHours(0, 0, 0, 0);
                if (expiryDate < tomorrow) {
                    return {
                        success: false,
                        error: 'Expiry date must be at least tomorrow',
                        statusCode: 400
                    };
                }
                // Validate userId format
                if (!mongoose_1.default.Types.ObjectId.isValid(userId)) {
                    return {
                        success: false,
                        error: 'Invalid user ID format',
                        statusCode: 400
                    };
                }
                // Create the item
                const fridgeItem = new schemas_1.FridgeItem({
                    userId: new mongoose_1.default.Types.ObjectId(userId),
                    name: itemData.name.trim(),
                    quantity: itemData.quantity,
                    unit: itemData.unit,
                    expiryDate,
                    isFavorite: itemData.isFavorite || false
                });
                const savedItem = yield fridgeItem.save();
                // Invalidate cache for user's fridge items and recommendations
                yield this.cacheService.invalidateFridgeItemsCache(userId);
                yield this.cacheService.delete(`recommendations:${userId}`);
                return {
                    success: true,
                    data: savedItem
                };
            }
            catch (error) {
                if (error.name === 'ValidationError') {
                    return {
                        success: false,
                        error: 'Validation failed',
                        statusCode: 400
                    };
                }
                return {
                    success: false,
                    error: error.message,
                    statusCode: 500
                };
            }
        });
    }
    /**
     * Update an existing fridge item
     */
    updateFridgeItem(userId, itemId, updateData) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                // Verify ownership and get item
                if (!mongoose_1.default.Types.ObjectId.isValid(userId)) {
                    return {
                        success: false,
                        error: 'Invalid user ID format',
                        statusCode: 400
                    };
                }
                if (!mongoose_1.default.Types.ObjectId.isValid(itemId)) {
                    return {
                        success: false,
                        error: 'Invalid item ID format',
                        statusCode: 400
                    };
                }
                const existingItem = yield schemas_1.FridgeItem.findOne({
                    _id: new mongoose_1.default.Types.ObjectId(itemId),
                    userId: new mongoose_1.default.Types.ObjectId(userId)
                });
                if (!existingItem) {
                    return {
                        success: false,
                        error: 'Fridge item not found or access denied',
                        statusCode: 404
                    };
                }
                // Validate expiry date if provided
                if (updateData.expiryDate) {
                    const expiryDate = new Date(updateData.expiryDate);
                    const tomorrow = new Date();
                    tomorrow.setDate(tomorrow.getDate() + 1);
                    tomorrow.setHours(0, 0, 0, 0);
                    if (expiryDate < tomorrow) {
                        return {
                            success: false,
                            error: 'Expiry date must be at least tomorrow',
                            statusCode: 400
                        };
                    }
                }
                // Prepare update object
                const updateObject = {};
                if (updateData.name !== undefined)
                    updateObject.name = updateData.name.trim();
                if (updateData.quantity !== undefined)
                    updateObject.quantity = updateData.quantity;
                if (updateData.unit !== undefined)
                    updateObject.unit = updateData.unit;
                if (updateData.expiryDate !== undefined)
                    updateObject.expiryDate = new Date(updateData.expiryDate);
                if (updateData.isFavorite !== undefined)
                    updateObject.isFavorite = updateData.isFavorite;
                // Update the item
                const updatedItem = yield schemas_1.FridgeItem.findByIdAndUpdate(itemId, updateObject, { new: true, runValidators: true });
                // Invalidate cache for user's fridge items and recommendations
                yield this.cacheService.invalidateFridgeItemsCache(userId);
                yield this.cacheService.delete(`recommendations:${userId}`);
                return {
                    success: true,
                    data: updatedItem
                };
            }
            catch (error) {
                if (error.name === 'ValidationError') {
                    return {
                        success: false,
                        error: 'Validation failed',
                        statusCode: 400
                    };
                }
                return {
                    success: false,
                    error: error.message,
                    statusCode: 500
                };
            }
        });
    }
    /**
     * Delete a fridge item
     */
    deleteFridgeItem(userId, itemId) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                if (!mongoose_1.default.Types.ObjectId.isValid(userId) || !mongoose_1.default.Types.ObjectId.isValid(itemId)) {
                    return {
                        success: false,
                        error: 'Invalid ID format',
                        statusCode: 400
                    };
                }
                const deletedItem = yield schemas_1.FridgeItem.findOneAndDelete({
                    _id: new mongoose_1.default.Types.ObjectId(itemId),
                    userId: new mongoose_1.default.Types.ObjectId(userId)
                });
                if (!deletedItem) {
                    return {
                        success: false,
                        error: 'Fridge item not found or access denied',
                        statusCode: 404
                    };
                }
                // Invalidate cache for user's fridge items and recommendations
                yield this.cacheService.invalidateFridgeItemsCache(userId);
                yield this.cacheService.delete(`recommendations:${userId}`);
                return {
                    success: true,
                    data: { message: 'Fridge item deleted successfully' }
                };
            }
            catch (error) {
                return {
                    success: false,
                    error: error.message,
                    statusCode: 500
                };
            }
        });
    }
    /**
     * Toggle favorite status of a fridge item
     */
    toggleFavoriteStatus(userId, itemId) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                if (!mongoose_1.default.Types.ObjectId.isValid(userId) || !mongoose_1.default.Types.ObjectId.isValid(itemId)) {
                    return {
                        success: false,
                        error: 'Invalid ID format',
                        statusCode: 400
                    };
                }
                const item = yield schemas_1.FridgeItem.findOne({
                    _id: new mongoose_1.default.Types.ObjectId(itemId),
                    userId: new mongoose_1.default.Types.ObjectId(userId)
                });
                if (!item) {
                    return {
                        success: false,
                        error: 'Fridge item not found or access denied',
                        statusCode: 404
                    };
                }
                item.isFavorite = !item.isFavorite;
                const updatedItem = yield item.save();
                return {
                    success: true,
                    data: updatedItem
                };
            }
            catch (error) {
                return {
                    success: false,
                    error: error.message,
                    statusCode: 500
                };
            }
        });
    }
    /**
     * Get only favorite items for a user
     */
    getFavoriteItems(userId) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                if (!mongoose_1.default.Types.ObjectId.isValid(userId)) {
                    return {
                        success: false,
                        error: 'Invalid user ID format',
                        statusCode: 400
                    };
                }
                const favoriteItems = yield schemas_1.FridgeItem.find({
                    userId: new mongoose_1.default.Types.ObjectId(userId),
                    isFavorite: true
                })
                    .sort({ expiryDate: 1 })
                    .lean();
                return {
                    success: true,
                    data: favoriteItems
                };
            }
            catch (error) {
                return {
                    success: false,
                    error: error.message,
                    statusCode: 500
                };
            }
        });
    }
    /**
     * Get recommended items based on priority algorithm
     */
    getRecommendedItems(userId) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                // Check cache first
                const cachedRecommendations = yield this.cacheService.getCachedRecommendations(userId);
                if (cachedRecommendations) {
                    return {
                        success: true,
                        data: cachedRecommendations
                    };
                }
                const today = new Date();
                today.setHours(0, 0, 0, 0);
                const threeDaysFromNow = new Date(today);
                threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);
                const sevenDaysFromNow = new Date(today);
                sevenDaysFromNow.setDate(sevenDaysFromNow.getDate() + 7);
                // Validate userId format
                if (!mongoose_1.default.Types.ObjectId.isValid(userId)) {
                    return {
                        success: false,
                        error: 'Invalid user ID format',
                        statusCode: 400
                    };
                }
                // Get all user items
                const allItems = yield schemas_1.FridgeItem.find({
                    userId: new mongoose_1.default.Types.ObjectId(userId)
                }).lean();
                const recommendations = [];
                allItems.forEach((item) => {
                    const expiryDate = new Date(item.expiryDate);
                    const daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
                    // Priority 1: Items expiring within 3 days (highest priority)
                    if (expiryDate <= threeDaysFromNow) {
                        recommendations.push(Object.assign(Object.assign({}, this.formatFridgeItemResponse(item)), { priority: 'high', reason: daysUntilExpiry <= 0
                                ? 'This item has expired and should be used immediately or discarded'
                                : daysUntilExpiry === 1
                                    ? 'This item expires tomorrow'
                                    : `This item expires in ${daysUntilExpiry} days` }));
                    }
                    // Priority 2: Favorite items expiring within 7 days (medium priority)
                    else if (item.isFavorite && expiryDate <= sevenDaysFromNow) {
                        recommendations.push(Object.assign(Object.assign({}, this.formatFridgeItemResponse(item)), { priority: 'medium', reason: `Your favorite item expires in ${daysUntilExpiry} days` }));
                    }
                    // Priority 3: All other favorite items (low priority)
                    else if (item.isFavorite) {
                        recommendations.push(Object.assign(Object.assign({}, this.formatFridgeItemResponse(item)), { priority: 'low', reason: 'This is one of your favorite items' }));
                    }
                });
                // Sort by priority and then by expiry date
                recommendations.sort((a, b) => {
                    const priorityOrder = { high: 3, medium: 2, low: 1 };
                    const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
                    if (priorityDiff !== 0)
                        return priorityDiff;
                    // If same priority, sort by expiry date (ascending)
                    return new Date(a.expiryDate).getTime() - new Date(b.expiryDate).getTime();
                });
                // Cache the recommendations for 1 hour
                yield this.cacheService.cacheRecommendations(userId, recommendations, 60 * 60);
                return {
                    success: true,
                    data: recommendations
                };
            }
            catch (error) {
                return {
                    success: false,
                    error: error.message,
                    statusCode: 500
                };
            }
        });
    }
    /**
     * Helper method to format fridge item for API response
     */
    formatFridgeItemResponse(item) {
        var _a;
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        // Safely handle expiry date calculations
        let daysUntilExpiry = null;
        let expiryStatus = 'fresh';
        if (item.expiryDate) {
            const expiryDate = new Date(item.expiryDate);
            expiryDate.setHours(0, 0, 0, 0);
            daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
            if (daysUntilExpiry < 0)
                expiryStatus = 'expired';
            else if (daysUntilExpiry === 0)
                expiryStatus = 'expires_today';
            else if (daysUntilExpiry <= 3)
                expiryStatus = 'expires_soon';
            else if (daysUntilExpiry <= 7)
                expiryStatus = 'expires_this_week';
        }
        return {
            id: ((_a = item._id) === null || _a === void 0 ? void 0 : _a.toString()) || null,
            name: item.name || '',
            quantity: item.quantity || 0,
            unit: item.unit || 'pieces',
            expiryDate: item.expiryDate ? item.expiryDate.toISOString() : null,
            isFavorite: item.isFavorite || false,
            createdAt: item.createdAt ? item.createdAt.toISOString() : null,
            updatedAt: item.updatedAt ? item.updatedAt.toISOString() : null,
            daysUntilExpiry,
            expiryStatus
        };
    }
}
exports.FridgeService = FridgeService;
