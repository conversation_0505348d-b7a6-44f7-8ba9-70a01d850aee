import React from 'react';
import { Pressable, StyleSheet, Text, View } from 'react-native';

interface ButtonGlobalProps {
    text: string;
    onPress?: () => void;
    style?: object;
    styleText?: object;
    icon?: any;
}

const ButtonIcon = ({ text, onPress, style,styleText, icon }: ButtonGlobalProps) => {
    return (
        <Pressable
            onPress={
                onPress &&
                onPress
            }
            style={[
                styles.container,
                style,
            ]}
        >
            <View style={{ position: 'absolute', left: 15 }}>
                {icon}
            </View>
            <Text style={[styleText, {  color: 'black', textAlign: 'center', transform: [{translateX: 8}] }]}>{text}</Text>
        </Pressable>

    )
}

export default ButtonIcon

const styles = StyleSheet.create({
    container: {
        borderWidth: 1,
        borderColor: '#00000020',
        width: '100%',
        height: 55,
        borderRadius: 30,
        alignItems: 'center',
        justifyContent: 'center',
        display: 'flex',
        flexDirection: 'row',
        position: 'relative',
    }


})        