// Common API response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  timestamp?: string;
}

export interface ApiErrorResponse {
  success: false;
  statusCode: number;
  message: string;
  details?: string[] | Record<string, any>;
  timestamp: string;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: PaginationMeta;
}

// Request with authenticated user
export interface AuthenticatedRequest extends Request {
  user: {
    _id: string;
    email: string;
    user: {
      name: string;
      surname: string;
      username: string;
      profile_picture: string;
      birthdate: Date | string;
      type: 'user' | 'admin' | 'operator';
    };
    finances: {
      subscription_plan: 'free' | 'hazel_c_01' | 'hazel_c_12';
      subscription_expiry: Date;
    };
    booleans: {
      isVerified: boolean;
      isAdmin: boolean;
    };
    createdAt: Date;
  };
}

// Validation error details
export interface ValidationError {
  field: string;
  message: string;
  code: string;
  value?: any;
}

export interface ValidationErrorResponse extends ApiErrorResponse {
  statusCode: 400;
  errors: ValidationError[];
}

// Rate limiting types
export interface RateLimitInfo {
  limit: number;
  remaining: number;
  resetTime: Date;
  retryAfter?: number;
}

export interface RateLimitErrorResponse extends ApiErrorResponse {
  statusCode: 429;
  rateLimitInfo: RateLimitInfo;
}

// Service response types
export interface ServiceResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  statusCode?: number;
}

// Database query options
export interface QueryOptions {
  page?: number;
  limit?: number;
  sort?: Record<string, 1 | -1>;
  populate?: string | string[];
  select?: string;
}

// Common filter types
export interface DateRangeFilter {
  from?: Date;
  to?: Date;
}

export interface SearchFilter {
  search?: string;
  fields?: string[];
}

// User tier mapping
export type UserTier = 'FREE' | 'PRO';

export interface UserTierLimits {
  FREE: {
    dailyRecipeGeneration: 5;
    apiRateLimit: 100; // per 15 minutes
  };
  PRO: {
    dailyRecipeGeneration: 15;
    apiRateLimit: 200; // per 15 minutes
  };
}

// Notification types
export interface NotificationPayload {
  title: string;
  body: string;
  data?: Record<string, any>;
}

export interface PushNotificationRequest {
  userId: string;
  payload: NotificationPayload;
  scheduleTime?: Date;
}

// CRON job types
export interface CronJobConfig {
  name: string;
  schedule: string; // cron expression
  enabled: boolean;
  timezone?: string;
}

export interface RecipeGenerationJobData {
  userId: string;
  expiringItemIds: string[];
  userTier: UserTier;
}

// Cache types
export interface CacheOptions {
  ttl?: number; // Time to live in seconds
  key: string;
}

export interface CachedRecipe {
  recipe: any;
  cachedAt: Date;
  expiresAt: Date;
}
