import Bull, { Queue } from 'bull';
import Redis from 'ioredis';

// Redis connection configuration
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  db: parseInt(process.env.REDIS_DB || '0'),
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  maxRetriesPerRequest: null,
};

// Create Redis connection
export const redis = new Redis(redisConfig);

// Job queue configurations
const queueConfig = {
  redis: redisConfig,
  defaultJobOptions: {
    removeOnComplete: 10, // Keep only 10 completed jobs
    removeOnFail: 50, // Keep 50 failed jobs for debugging
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 2000,
    },
  },
};

// Create job queues
export const recipeGenerationQueue = new Bull('recipe-generation', queueConfig);
export const notificationQueue = new Bull('notifications', queueConfig);
export const cleanupQueue = new Bull('cleanup', queueConfig);

// Queue event handlers for monitoring
const setupQueueEvents = (queue: Queue, queueName: string) => {
  queue.on('completed', (job) => {
    console.log(`[${queueName}] Job ${job.id} completed successfully`);
  });

  queue.on('failed', (job, err) => {
    console.error(`[${queueName}] Job ${job.id} failed:`, err.message);
  });

  queue.on('stalled', (job) => {
    console.warn(`[${queueName}] Job ${job.id} stalled`);
  });

  queue.on('error', (err) => {
    console.error(`[${queueName}] Queue error:`, err);
  });

  queue.on('progress', (job, progress) => {
    let progressText: string;
    if (typeof progress === 'number') {
      progressText = `${progress}%`;
    } else {
      progressText = typeof progress === 'object' ? JSON.stringify(progress) : String(progress);
    }
    console.log(`[${queueName}] Job ${job.id} progress: ${progressText}`);
  });
};

// Setup event handlers for all queues
setupQueueEvents(recipeGenerationQueue, 'RecipeGeneration');
setupQueueEvents(notificationQueue, 'Notifications');
setupQueueEvents(cleanupQueue, 'Cleanup');

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('Shutting down job queues...');
  await Promise.all([
    recipeGenerationQueue.close(),
    notificationQueue.close(),
    cleanupQueue.close(),
  ]);
  await redis.disconnect();
  console.log('Job queues shut down successfully');
});

export default {
  recipeGenerationQueue,
  notificationQueue,
  cleanupQueue,
  redis,
};
