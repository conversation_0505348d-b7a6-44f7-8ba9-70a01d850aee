import { Ingredient } from "@/types/ingredient"

export const calculateFreshnessPercentage = (ingredient: Ingredient): number => {
    const now = new Date()
    const added = new Date(ingredient.addedDate)
    const expiry = new Date(ingredient.expiryDate)
    const totalTime = expiry.getTime() - added.getTime()
    const remainingTime = expiry.getTime() - now.getTime()
    const percentage = Math.max(0, Math.min(100, (remainingTime / totalTime) * 100))
    return Math.round(percentage)
}

export const getFreshnessColor = (percentage: number): string => {
    if (percentage > 70) return "#8BC34A"
    if (percentage > 30) return "#FF9800"
    return "#F44336"
}

export const getFreshnessLabel = (percentage: number): string => {
    if (percentage > 70) return "Fresh"
    if (percentage > 30) return "Use Soon"
    return "Expires Soon"
}