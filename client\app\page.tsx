import Link from "next/link"
import Image from "next/image"

import { Clock, Utensils, Leaf, ArrowR<PERSON>, ShoppingCart } from 'lucide-react'
import Button from "@/components/global/button"


export default function LandingPage() {
  

  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-14 items-center px-4">
          <div className="flex items-center space-x-2">
            <Leaf className="h-6 w-6 text-green-500" />
            <span className="font-bold text-xl text-[#000000]">LeftoverChef</span>
          </div>
          <nav className="ml-auto flex gap-4 sm:gap-6">
           {/*  <Link 
              href="#features" 
              className="text-sm font-medium text-muted-foreground transition-colors hover:text-foreground"
            >
              Features
            </Link>
            <Link 
              href="#how-it-works" 
              className="text-sm font-medium text-muted-foreground transition-colors hover:text-foreground"
            >
              How It Works
            </Link>
            <Link 
              href="#testimonials" 
              className="text-sm font-medium text-muted-foreground transition-colors hover:text-foreground"
            >
              Testimonials
            </Link> */}
          </nav>
        </div>
      </header>

      <main className="flex-1">
        {/* Hero Section */}
        <section className="px-4 py-12 md:py-24 lg:py-32">
          <div className="container flex flex-col items-center text-center">
            <div className="space-y-4 mb-8">
              <h1 className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl text-[#000000]">
                Never Waste Food <span className="text-green-500">Again</span>
              </h1>
              <p className="mx-auto max-w-[700px] text-lg text-muted-foreground md:text-xl text-[#0000005e]">
                Track when food is about to expire and get delicious recipe suggestions based on what&apos;s already in your fridge.
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-4 mb-12">
              <Button
                variant="primary"
                button={{ color: '#00c951' }}
                text={{ color: 'white', size: 'lg' }}
                href="/auth"
              >
                Get Started
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                href="#features"
              >
                Learn More
              </Button>
            </div>
            <div className="relative w-full max-w-3xl mx-auto rounded-lg overflow-hidden shadow-xl flex items-center justify-center">
              <Image
                src="/images/leftover-chef_basket.png"
                width={250}
                height={250}
                alt="LeftoverChef app interface showing recipe suggestions"
                className="w-[280px] object-cover mb-8"
              />
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="bg-muted py-12 md:py-24">
          <div className="container px-4">
            <h2 className="text-3xl font-bold tracking-tighter text-center mb-12 sm:text-4xl text-[#000000]">
              Smart Food Management
            </h2>
            <div className="grid gap-8 md:grid-cols-3 text-[#000000]">
              <div className="flex flex-col items-center text-center p-6 bg-background rounded-lg shadow-sm">
                <div className="rounded-full bg-green-100 p-3 mb-4">
                  <Clock className="h-6 w-6 text-green-500" />
                </div>
                <h3 className="text-xl font-bold mb-2">Expiration Tracking</h3>
                <p className="text-muted-foreground text-[#0000005e]">
                  Get timely notifications when your food is about to expire so nothing goes to waste.
                </p>
              </div>
              <div className="flex flex-col items-center text-center p-6 bg-background rounded-lg shadow-sm">
                <div className="rounded-full bg-green-100 p-3 mb-4">
                  <Utensils className="h-6 w-6 text-green-500" />
                </div>
                <h3 className="text-xl font-bold mb-2">Recipe Suggestions</h3>
                <p className="text-muted-foreground text-[#0000005e]">
                  Discover delicious recipes based on the ingredients you already have in your fridge.
                </p>
              </div>
              <div className="flex flex-col items-center text-center p-6 bg-background rounded-lg shadow-sm">
                <div className="rounded-full bg-green-100 p-3 mb-4">
                  <ShoppingCart className="h-6 w-6 text-green-500" />
                </div>
                <h3 className="text-xl font-bold mb-2">Smart Shopping Lists</h3>
                <p className="text-muted-foreground text-[#0000005e]">
                  Generate shopping lists based on what you need and what you already have.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* How It Works Section */}
        {/* <section id="how-it-works" className="py-12 md:py-24">
          <div className="container px-4">
            <h2 className="text-3xl font-bold tracking-tighter text-center mb-12 sm:text-4xl">
              How It Works
            </h2>
            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
              <div className="flex flex-col p-6 border rounded-lg">
                <div className="flex items-center mb-4">
                  <div className="rounded-full bg-green-100 p-2 mr-4">
                    <span className="font-bold text-green-500">1</span>
                  </div>
                  <h3 className="text-xl font-bold">Add Your Ingredients</h3>
                </div>
                <p className="text-muted-foreground mb-4">
                  Scan barcodes or manually add items to your virtual fridge with expiration dates.
                </p>
                <Image
                  src="/placeholder.svg?height=200&width=300"
                  width={300}
                  height={200}
                  alt="Adding ingredients to the app"
                  className="rounded-md mt-auto"
                />
              </div>
              <div className="flex flex-col p-6 border rounded-lg">
                <div className="flex items-center mb-4">
                  <div className="rounded-full bg-green-100 p-2 mr-4">
                    <span className="font-bold text-green-500">2</span>
                  </div>
                  <h3 className="text-xl font-bold">Get Notifications</h3>
                </div>
                <p className="text-muted-foreground mb-4">
                  Receive timely alerts when food is approaching its expiration date.
                </p>
                <Image
                  src="/placeholder.svg?height=200&width=300"
                  width={300}
                  height={200}
                  alt="Notification screen"
                  className="rounded-md mt-auto"
                />
              </div>
              <div className="flex flex-col p-6 border rounded-lg">
                <div className="flex items-center mb-4">
                  <div className="rounded-full bg-green-100 p-2 mr-4">
                    <span className="font-bold text-green-500">3</span>
                  </div>
                  <h3 className="text-xl font-bold">Discover Recipes</h3>
                </div>
                <p className="text-muted-foreground mb-4">
                  Browse recipe suggestions tailored to the ingredients you need to use up.
                </p>
                <Image
                  src="/placeholder.svg?height=200&width=300"
                  width={300}
                  height={200}
                  alt="Recipe suggestion screen"
                  className="rounded-md mt-auto"
                />
              </div>
            </div>
          </div>
        </section> */}

        {/* Stats Section */}
        <section className="bg-green-50 py-12 md:py-24">
          <div className="container px-4">
            <div className="grid gap-8 md:grid-cols-3">
              <div className="flex flex-col items-center text-center">
                <span className="text-4xl font-bold text-green-500 mb-2">30%</span>
                <p className="text-muted-foreground text-[#0000005e]">
                  Average reduction in food waste for our users
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <span className="text-4xl font-bold text-green-500 mb-2">1000+</span>
                <p className="text-muted-foreground text-[#0000005e]">
                  Recipes in our database
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <span className="text-4xl font-bold text-green-500 mb-2">$720</span>
                <p className="text-muted-foreground text-[#0000005e]">
                  Average annual savings per household
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section id="testimonials" className="py-12 md:py-24">
          <div className="container px-4">
            <h2 className="text-3xl font-bold tracking-tighter text-center mb-12 sm:text-4xl text-[#000000]">
              What Our Users Say
            </h2>
            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
              <div className="p-6 border rounded-lg">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 rounded-full bg-green-100 mr-4"></div>
                  <div>
                    <h4 className="font-bold text-[#000000]">Sarah K.</h4>
                    <div className="flex text-yellow-400">
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current" />
                    </div>
                  </div>
                </div>
                <p className="text-muted-foreground text-[#0000005e]">
                &quot;LeftoverChef has completely changed how I manage my kitchen. I&apos;ve saved so much money by not throwing away food!&quot;
                </p>
              </div>
              <div className="p-6 border rounded-lg">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 rounded-full bg-green-100 mr-4"></div>
                  <div>
                    <h4 className="font-bold text-[#000000]">Michael T.</h4>
                    <div className="flex text-yellow-400">
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current" />
                    </div>
                  </div>
                </div>
                <p className="text-muted-foreground text-[#0000005e]">
                &quot;The recipe suggestions are amazing! I&apos;ve discovered so many new dishes using ingredients I would have otherwise thrown away.&quot;
                </p>
              </div>
              <div className="p-6 border rounded-lg">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 rounded-full bg-green-100 mr-4"></div>
                  <div>
                    <h4 className="font-bold text-[#000000]">Jessica R.</h4>
                    <div className="flex text-yellow-400">
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current" />
                    </div>
                  </div>
                </div>
                <p className="text-muted-foreground text-[#0000005e]">
                &quot;As a busy parent, this app has been a lifesaver. I always know what&apos;s in my fridge and when it needs to be used.&quot;
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="bg-green-500 text-white py-12 md:py-24">
          <div className="container px-4 text-center">
            <h2 className="text-3xl font-bold tracking-tighter mb-4 sm:text-4xl">
              Ready to Reduce Food Waste?
            </h2>
            <p className="mx-auto max-w-[600px] mb-8 text-green-50">
              Join thousands of users who are saving money and reducing their environmental impact.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Button variant="primary" button={{color: 'white'}} text={{color: 'black'}}>
                Register Now
              </Button>
            </div>
          </div>
        </section>
      </main>

      <footer className="border-t py-6 md:py-8">
        <div className="container px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <Leaf className="h-5 w-5 text-green-500" />
              <span className="font-bold text-[#000000]">LeftoverChef</span>
            </div>
            <div className="flex flex-col md:flex-row gap-4 md:gap-8 text-center md:text-left text-[#00000050]">
              <Link href="#" className="text-sm text-muted-foreground hover:text-foreground">
                Privacy Policy
              </Link>
              <Link href="#" className="text-sm text-muted-foreground hover:text-foreground">
                Terms of Service
              </Link>
              <Link href="#" className="text-sm text-muted-foreground hover:text-foreground">
                Contact Us
              </Link>
            </div>
            <div className="mt-4 md:mt-0 text-sm text-muted-foreground text-[#000000]">
              © {new Date().getFullYear()} Powered by Hazel
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}

function Star(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" />
    </svg>
  )
}
