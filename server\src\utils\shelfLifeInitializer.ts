import { seedShelfLifeData } from '../database/seedData/shelfLifeData';
import { FoodShelfLife } from '../database/schemas';

/**
 * Initialize the shelf life system
 */
export const initializeShelfLifeSystem = async (): Promise<void> => {
  try {
    console.log('Initializing shelf life system...');

    // Check if we already have shelf life data
    const existingCount = await FoodShelfLife.countDocuments();
    
    if (existingCount === 0) {
      console.log('No existing shelf life data found, seeding database...');
      await seedShelfLifeData();
    } else {
      console.log(`Found ${existingCount} existing shelf life records, skipping seed`);
    }

    // Create indexes if they don't exist
    await ensureIndexes();

    console.log('Shelf life system initialized successfully');
  } catch (error: any) {
    console.error('Failed to initialize shelf life system:', error.message);
    throw error;
  }
};

/**
 * Ensure all required indexes exist
 */
async function ensureIndexes(): Promise<void> {
  try {
    console.log('Ensuring shelf life indexes...');
    
    // The indexes are defined in the schema, but we can ensure they exist
    await FoodShelfLife.collection.createIndex({ normalizedFoodName: 1 });
    await FoodShelfLife.collection.createIndex({ foodName: 'text' });
    await FoodShelfLife.collection.createIndex({ category: 1, normalizedFoodName: 1 });
    await FoodShelfLife.collection.createIndex({ source: 1, confidence: -1 });
    await FoodShelfLife.collection.createIndex({ createdAt: -1 });
    
    console.log('Shelf life indexes ensured');
  } catch (error: any) {
    console.warn('Failed to ensure some indexes:', error.message);
    // Don't throw here as the system can still work without perfect indexes
  }
}

/**
 * Get shelf life system statistics
 */
export const getShelfLifeStats = async (): Promise<{
  totalFoods: number;
  bySource: Record<string, number>;
  byCategory: Record<string, number>;
  averageConfidence: number;
}> => {
  try {
    const [totalFoods, bySource, byCategory, avgConfidence] = await Promise.all([
      FoodShelfLife.countDocuments(),
      FoodShelfLife.aggregate([
        { $group: { _id: '$source', count: { $sum: 1 } } }
      ]),
      FoodShelfLife.aggregate([
        { $group: { _id: '$category', count: { $sum: 1 } } }
      ]),
      FoodShelfLife.aggregate([
        { $group: { _id: null, avgConfidence: { $avg: '$confidence' } } }
      ])
    ]);

    const sourceStats: Record<string, number> = {};
    bySource.forEach((item: any) => {
      sourceStats[item._id] = item.count;
    });

    const categoryStats: Record<string, number> = {};
    byCategory.forEach((item: any) => {
      categoryStats[item._id || 'uncategorized'] = item.count;
    });

    return {
      totalFoods,
      bySource: sourceStats,
      byCategory: categoryStats,
      averageConfidence: avgConfidence[0]?.avgConfidence || 0
    };
  } catch (error: any) {
    console.error('Failed to get shelf life stats:', error.message);
    return {
      totalFoods: 0,
      bySource: {},
      byCategory: {},
      averageConfidence: 0
    };
  }
};
