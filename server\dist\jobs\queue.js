"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.cleanupQueue = exports.notificationQueue = exports.recipeGenerationQueue = exports.redis = void 0;
const tslib_1 = require("tslib");
const bull_1 = tslib_1.__importDefault(require("bull"));
const ioredis_1 = tslib_1.__importDefault(require("ioredis"));
// Redis connection configuration
const redisConfig = {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB || '0'),
    retryDelayOnFailover: 100,
    enableReadyCheck: false,
    maxRetriesPerRequest: null,
};
// Create Redis connection
exports.redis = new ioredis_1.default(redisConfig);
// Job queue configurations
const queueConfig = {
    redis: redisConfig,
    defaultJobOptions: {
        removeOnComplete: 10, // Keep only 10 completed jobs
        removeOnFail: 50, // Keep 50 failed jobs for debugging
        attempts: 3,
        backoff: {
            type: 'exponential',
            delay: 2000,
        },
    },
};
// Create job queues
exports.recipeGenerationQueue = new bull_1.default('recipe-generation', queueConfig);
exports.notificationQueue = new bull_1.default('notifications', queueConfig);
exports.cleanupQueue = new bull_1.default('cleanup', queueConfig);
// Queue event handlers for monitoring
const setupQueueEvents = (queue, queueName) => {
    queue.on('completed', (job) => {
        console.log(`[${queueName}] Job ${job.id} completed successfully`);
    });
    queue.on('failed', (job, err) => {
        console.error(`[${queueName}] Job ${job.id} failed:`, err.message);
    });
    queue.on('stalled', (job) => {
        console.warn(`[${queueName}] Job ${job.id} stalled`);
    });
    queue.on('error', (err) => {
        console.error(`[${queueName}] Queue error:`, err);
    });
    queue.on('progress', (job, progress) => {
        let progressText;
        if (typeof progress === 'number') {
            progressText = `${progress}%`;
        }
        else {
            progressText = typeof progress === 'object' ? JSON.stringify(progress) : String(progress);
        }
        console.log(`[${queueName}] Job ${job.id} progress: ${progressText}`);
    });
};
// Setup event handlers for all queues
setupQueueEvents(exports.recipeGenerationQueue, 'RecipeGeneration');
setupQueueEvents(exports.notificationQueue, 'Notifications');
setupQueueEvents(exports.cleanupQueue, 'Cleanup');
// Graceful shutdown
process.on('SIGTERM', () => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    console.log('Shutting down job queues...');
    yield Promise.all([
        exports.recipeGenerationQueue.close(),
        exports.notificationQueue.close(),
        exports.cleanupQueue.close(),
    ]);
    yield exports.redis.disconnect();
    console.log('Job queues shut down successfully');
}));
exports.default = {
    recipeGenerationQueue: exports.recipeGenerationQueue,
    notificationQueue: exports.notificationQueue,
    cleanupQueue: exports.cleanupQueue,
    redis: exports.redis,
};
