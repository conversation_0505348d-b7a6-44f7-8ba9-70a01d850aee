import React from 'react'
import { StyleSheet } from 'react-native'
import Svg, { <PERSON>, <PERSON> } from 'react-native-svg'

const EdamameIcon = () => {
    return (

        <Svg id="Capa_1" enable-background="new 0 0 511.732 511.732" height="50" viewBox="0 0 511.732 511.732" width="50">
            <G id="XMLID_745_">
                <Path id="XMLID_749_" d="m168.047 198.62c-10.485 10.485-17.466 23.477-19.658 36.583-2.465 14.747 1.457 28.541 10.761 37.844 7.48 7.481 17.862 11.482 29.31 11.482 2.791 0 5.645-.238 8.535-.721 13.105-2.191 26.098-9.172 36.583-19.658 23.362-23.362 27.27-56.054 8.896-74.427-18.372-18.373-51.066-14.463-74.427 8.897zm44.318 44.318c-6.017 6.017-13.422 10.128-20.316 11.281-2.976.497-8.45.849-11.685-2.385-3.234-3.233-2.882-8.708-2.385-11.684 1.153-6.895 5.265-14.299 11.281-20.316 7.84-7.84 17.087-11.614 23.979-11.614 3.396 0 6.222.917 8.022 2.718 5.453 5.452 2.807 20.296-8.896 32z" />
                <Path id="XMLID_766_" d="m276.317 176.643c8.013 11.322 21.116 17.073 35.696 17.073 12.733 0 26.593-4.389 39.165-13.286 12.104-8.565 21.177-20.193 25.548-32.741 4.918-14.119 3.38-28.377-4.221-39.118-7.602-10.741-20.535-16.934-35.486-16.992-.073 0-.145 0-.217 0-13.223 0-27.121 4.685-39.158 13.204-26.967 19.085-36.335 50.649-21.327 71.86zm38.658-47.371c6.912-4.892 14.861-7.692 21.827-7.692h.101c3.018.012 8.474.589 11.115 4.322 2.642 3.733 1.371 9.07.379 11.919-2.3 6.602-7.602 13.207-14.548 18.122-13.51 9.561-28.588 9.666-33.043 3.371-4.454-6.296.658-20.481 14.169-30.042z" />
                <Path id="XMLID_1246_" d="m143.492 303.52c-20.847-15.512-52.625-6.901-72.348 19.604-8.853 11.896-13.875 25.764-14.14 39.048-.299 14.948 5.583 28.027 16.139 35.882 7.006 5.214 15.247 7.702 23.851 7.702 16.994 0 35.403-9.71 48.498-27.306 19.723-26.504 18.845-59.417-2-74.93zm-22.068 57.021c-9.881 13.278-24.184 18.051-30.371 13.447-3.669-2.73-4.116-8.198-4.056-11.215.14-6.989 3.134-14.912 8.214-21.738 7.427-9.981 17.352-15.155 24.451-15.155 2.346 0 4.383.565 5.92 1.708 6.187 4.604 5.723 19.675-4.158 32.953z" />
                <Path id="XMLID_1249_" d="m434.446 56.378c-9.799-3.471-30.64-10.349-55.047-15.408-46.632-9.667-82.309-7.832-106.034 5.455-20.79 11.643-37.403 28.412-48.043 48.494-8.097 15.281-22.204 25.775-38.706 28.793-24.235 4.432-46.518 16.247-64.439 34.168-16.014 16.014-27.292 35.807-32.616 57.237-3.917 15.768-13.911 28.467-28.14 35.758-20.697 10.605-37.941 27.467-49.868 48.763-13.288 23.727-15.123 59.402-5.455 106.036 6.89 33.234 17.159 59.882 17.593 61l3.951 10.186 10.907-.636c1.251-.073 31.021-1.88 64.69-9.983 47.7-11.479 78.78-29.609 92.376-53.887 6.663-11.897 11.381-24.672 14.022-37.968 3.36-16.909 14.375-30.823 30.22-38.173 13.193-6.12 25.189-14.529 35.657-24.996 10.485-10.487 18.907-22.51 25.031-35.734 7.479-16.15 22.71-27.071 41.788-29.96 15.375-2.329 30.093-7.387 43.746-15.034 24.278-13.596 42.409-44.676 53.888-92.376 5.467-22.716 8.065-43.643 9.201-55.248 11.1-4.062 33.187-10.154 56.16-5.134l6.404-29.309c-33.573-7.337-64.419 2.713-77.286 7.956zm-23.746 75.124c-9.176 37.906-23.126 63.764-39.279 72.81h-.001c-10.491 5.876-21.789 9.761-33.579 11.547-28.786 4.36-52.905 21.937-64.519 47.016-4.638 10.015-11.038 19.142-19.022 27.127-7.97 7.969-17.077 14.359-27.068 18.994-24.633 11.427-41.771 33.129-47.02 59.541-2.027 10.202-5.651 20.012-10.772 29.156-9.046 16.153-34.904 30.103-72.81 39.279-19.331 4.68-37.624 7.128-48.776 8.315-11.306-33.304-27.839-99.362-10.126-130.991 9.029-16.123 21.953-28.821 37.374-36.723 21.741-11.141 37.624-31.269 43.575-55.226 4.009-16.139 12.555-31.097 24.713-43.256 13.594-13.594 30.408-22.54 48.623-25.871 25.592-4.68 47.395-20.812 59.818-44.258 7.933-14.973 20.449-27.548 36.193-36.365 9.973-5.585 23.395-7.755 38.143-7.755 31.921 0 70.053 10.168 92.848 17.89-1.189 11.157-3.636 29.445-8.315 48.77z" />
            </G>
        </Svg>
    )
}

export default EdamameIcon

const styles = StyleSheet.create({})
