import React, { useEffect, useRef } from 'react';
import { Animated, StyleSheet, View, Dimensions, PanResponder, TouchableOpacity } from 'react-native';


interface BottomSheetProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  height: number;
}

const BottomSheet: React.FC<BottomSheetProps> = ({ isOpen, children, onClose, height }) => {
  const translateY = useRef(new Animated.Value(height)).current;

  // Animate the bottom sheet based on the isOpen prop
  useEffect(() => {
    Animated.spring(translateY, {
      toValue: isOpen ? 0 : height,
      tension: 30,
      friction: 8,
      useNativeDriver: true,
    }).start();
  }, [isOpen, height]);

  // Define the pan responder for dragging the bottom sheet
  const panResponder = useRef(
    PanResponder.create({
      //onMoveShouldSetPanResponder: () => true,
      onPanResponderMove: (event, gestureState) => {
        //translateY.setValue(Math.max(Math.min(gestureState.dy, height), 0));
      },
      onPanResponderRelease: (event, gestureState) => {
        return
        if (gestureState.dy > height * 0.3) {
          Animated.spring(translateY, {
            toValue: height,
            tension: 70,
            friction: 8,
            useNativeDriver: true,
          }).start(() => onClose());
        } else {
          Animated.spring(translateY, {
            toValue: 0,
            tension: 70,
            friction: 8,
            useNativeDriver: true,
          }).start();
        }
      },
    })
  ).current;

  // Interpolate overlay opacity based on the bottom sheet's translateY value
  const overlayOpacity = translateY.interpolate({
    inputRange: [0, height],
    outputRange: [1, 0],
    extrapolate: 'clamp',
  });

  return (
    <>
      <Animated.View style={[styles.overlay, { opacity: overlayOpacity }]} />
      <Animated.View
        style={[
          styles.bottomSheet,
          { height, transform: [{ translateY }] },
        ]}
        /* {...panResponder.panHandlers} */
      >
        <TouchableOpacity onPress={()=>{onClose()}} style={{width: '100%', height: 20}}>
            <View style={styles.handle} />
        </TouchableOpacity>
        <View style={styles.content}>
          {children}
        </View>
      </Animated.View>
    </>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.5)',
    zIndex: 2000000
  },
  bottomSheet: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    shadowColor: '#00000090',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.2,
    shadowRadius: 10,
    elevation: 5,
    zIndex: 2000002
  },
  handle: {
    width: 40,
    height: 5,
    backgroundColor: '#ccc',
    alignSelf: 'center',
    marginTop: 10,
    borderRadius: 2.5,
  },
  content: {
    flex: 1,
    padding: 20,
  },
});

export default BottomSheet;