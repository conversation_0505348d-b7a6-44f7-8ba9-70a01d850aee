"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const middleware_1 = require("../middleware");
const fridge_1 = require("../controllers/fridge");
const router = (0, express_1.Router)();
// Apply authentication and rate limiting middleware to all routes
router.use(middleware_1.AuthenticateTokenOAuth);
router.use(middleware_1.fridgeOperationsRateLimit);
// GET /fridge - Get all fridge items with pagination and filtering
router.get('/', middleware_1.validateFridgeItemsQuery, middleware_1.validatePagination, fridge_1.getFridgeItems);
// POST /fridge - Create new fridge item
router.post('/', middleware_1.validateCreateFridgeItem, fridge_1.createFridgeItem);
// PUT /fridge/:id - Update specific fridge item
router.put('/:id', middleware_1.validateMongoId, middleware_1.validateUpdateFridgeItem, fridge_1.updateFridgeItem);
// DELETE /fridge/:id - Delete specific fridge item
router.delete('/:id', middleware_1.validateMongoId, fridge_1.deleteFridgeItem);
// POST /fridge/:id/favorite - Toggle favorite status
router.post('/:id/favorite', middleware_1.validateMongoId, fridge_1.toggleFavorite);
// GET /fridge/favorites - Get only favorite items
router.get('/favorites', fridge_1.getFavoriteItems);
// GET /fridge/recommendations - Get recommended items
router.get('/recommendations', fridge_1.getRecommendations);
exports.default = router;
