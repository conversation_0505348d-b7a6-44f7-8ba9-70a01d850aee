import mongoose, { Document, Schema } from 'mongoose';

// Milliseconds in one day (avoid magic number 86_400_000)
const DAY_MS = 24 * 60 * 60 * 1000; // 86_400_000

export interface IFridgeItem extends Document {
  userId: mongoose.Types.ObjectId;
  name: string;
  quantity: number;
  unit: 'pieces' | 'kg' | 'g' | 'l' | 'ml' | 'cups' | 'tbsp' | 'tsp';
  expiryDate: Date;
  isFavorite: boolean;
  createdAt: Date;
  updatedAt: Date;
  daysUntilExpiry: number;
  expiryStatus: 'expired' | 'expires_today' | 'expires_tomorrow' | 'expires_soon' | 'expires_this_week' | 'fresh';
}

const fridgeItemSchema = new mongoose.Schema<IFridgeItem>({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Account',
    required: true,
    index: true
  },
  name: {
    type: String,
    required: [true, 'Item name is required'],
    trim: true,
    maxlength: [100, 'Item name cannot exceed 100 characters'],
    minlength: [1, 'Item name cannot be empty']
  },
  quantity: {
    type: Number,
    required: [true, 'Quantity is required'],
    min: [0.01, 'Quantity must be at least 0.01'],
    validate: {
      validator: function(value: number) {
        // Check if the number has at most 2 decimal places
        return Number(value.toFixed(2)) === value;
      },
      message: 'Quantity can have at most 2 decimal places'
    }
  },
  unit: {
    type: String,
    required: [true, 'Unit is required'],
    enum: {
      values: ['pieces', 'kg', 'g', 'l', 'ml', 'cups', 'tbsp', 'tsp'],
      message: 'Unit must be one of: pieces, kg, g, l, ml, cups, tbsp, tsp'
    }
  },
  expiryDate: {
    type: Date,
    required: [true, 'Expiry date is required'],
    validate: {
      validator: function(value: Date) {
        // Normalize current time to UTC midnight and require expiry >= tomorrow (UTC)
        const now = new Date();
        const todayUtcMs = Date.UTC(
          now.getUTCFullYear(),
          now.getUTCMonth(),
          now.getUTCDate()
        );
        const tomorrowUtcMs = todayUtcMs + DAY_MS;

        // Normalize provided expiry date to its UTC midnight
        const exp = new Date(value);
        const expiryUtcMs = Date.UTC(
          exp.getUTCFullYear(),
          exp.getUTCMonth(),
          exp.getUTCDate()
        );

        return expiryUtcMs >= tomorrowUtcMs; // must be at least tomorrow (UTC)
      },
      message: 'Expiry date must be at least tomorrow'
    }
  },
  isFavorite: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true, // This automatically adds createdAt and updatedAt
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Compound index for efficient queries
fridgeItemSchema.index({ userId: 1, expiryDate: 1 });
fridgeItemSchema.index({ userId: 1, isFavorite: 1 });
fridgeItemSchema.index({ userId: 1, createdAt: -1 });

// Virtual for days until expiry
fridgeItemSchema.virtual('daysUntilExpiry').get(function (this: IFridgeItem) {
  const now = new Date();
  const todayUTC = Date.UTC(
    now.getUTCFullYear(),
    now.getUTCMonth(),
    now.getUTCDate()
  );
  const exp = new Date(this.expiryDate);
  const expiryUTC = Date.UTC(
    exp.getUTCFullYear(),
    exp.getUTCMonth(),
    exp.getUTCDate()
  );
  // Exact calendar-day delta; negative for past dates
  return Math.round((expiryUTC - todayUTC) / DAY_MS);
});

// Virtual for expiry status
fridgeItemSchema.virtual('expiryStatus').get(function() {
  const daysUntilExpiry = this.daysUntilExpiry;
  if (daysUntilExpiry < 0) return 'expired';
  if (daysUntilExpiry === 0) return 'expires_today';
  if (daysUntilExpiry === 1) return 'expires_tomorrow';
  if (daysUntilExpiry <= 3) return 'expires_soon';
  if (daysUntilExpiry <= 7) return 'expires_this_week';
  return 'fresh';
});

export default mongoose.model<IFridgeItem>('FridgeItem', fridgeItemSchema);
