import mongoose, { Document, Schema } from 'mongoose';

export interface IFridgeItem extends Document {
  userId: mongoose.Types.ObjectId;
  name: string;
  quantity: number;
  unit: 'pieces' | 'kg' | 'g' | 'l' | 'ml' | 'cups' | 'tbsp' | 'tsp';
  expiryDate: Date;
  isFavorite: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const fridgeItemSchema = new mongoose.Schema<IFridgeItem>({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Account',
    required: true,
    index: true
  },
  name: {
    type: String,
    required: [true, 'Item name is required'],
    trim: true,
    maxlength: [100, 'Item name cannot exceed 100 characters'],
    minlength: [1, 'Item name cannot be empty']
  },
  quantity: {
    type: Number,
    required: [true, 'Quantity is required'],
    min: [0.01, 'Quantity must be at least 0.01'],
    validate: {
      validator: function(value: number) {
        // Check if the number has at most 2 decimal places
        return Number(value.toFixed(2)) === value;
      },
      message: 'Quantity can have at most 2 decimal places'
    }
  },
  unit: {
    type: String,
    required: [true, 'Unit is required'],
    enum: {
      values: ['pieces', 'kg', 'g', 'l', 'ml', 'cups', 'tbsp', 'tsp'],
      message: 'Unit must be one of: pieces, kg, g, l, ml, cups, tbsp, tsp'
    }
  },
  expiryDate: {
    type: Date,
    required: [true, 'Expiry date is required'],
    validate: {
      validator: function(value: Date) {
        // Check if expiry date is at least tomorrow
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(0, 0, 0, 0);
        return value >= tomorrow;
      },
      message: 'Expiry date must be at least tomorrow'
    }
  },
  isFavorite: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true, // This automatically adds createdAt and updatedAt
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Compound index for efficient queries
fridgeItemSchema.index({ userId: 1, expiryDate: 1 });
fridgeItemSchema.index({ userId: 1, isFavorite: 1 });
fridgeItemSchema.index({ userId: 1, createdAt: -1 });

// Virtual for days until expiry
fridgeItemSchema.virtual('daysUntilExpiry').get(function() {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const expiry = new Date(this.expiryDate);
  expiry.setHours(0, 0, 0, 0);
  const diffTime = expiry.getTime() - today.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
});

// Virtual for expiry status
fridgeItemSchema.virtual('expiryStatus').get(function() {
  const daysUntilExpiry = this.daysUntilExpiry;
  if (daysUntilExpiry < 0) return 'expired';
  if (daysUntilExpiry <= 1) return 'expires_today';
  if (daysUntilExpiry <= 3) return 'expires_soon';
  if (daysUntilExpiry <= 7) return 'expires_this_week';
  return 'fresh';
});

export default mongoose.model<IFridgeItem>('FridgeItem', fridgeItemSchema);
