import React from 'react'
import { StyleSheet } from 'react-native'
import Svg, { Path } from 'react-native-svg'

const NoodleIcon = () => {
    return (

        <Svg id="Capa_1" enable-background="new 0 0 511.004 511.004" height="50" viewBox="0 0 511.004 511.004" width="50">
            <Path id="XMLID_1087_" d="m511.004 256.469h-49.79c-7.125-40.685-42.707-71.704-85.401-71.704-8.551 0-16.893 1.224-24.925 3.637-24.286-30.049-61.424-49.303-102.979-49.303-13.218 0-26.393 1.998-38.972 5.858v-78.494h276.51v-30h-462.894v30h25.247v190.006h-47.8v30h30.644v8.326c0 99.112 80.634 179.746 179.746 179.746h90.225c99.112 0 179.746-80.634 179.746-179.746v-8.326h30.644v-30zm-135.191-41.704c26.074 0 48.08 17.696 54.674 41.704h-51.063c-1.648-14.548-5.663-28.387-11.651-41.125 2.643-.373 5.323-.579 8.04-.579zm-127.904-45.666c51.352 0 93.989 38.009 101.268 87.37h-35.692c-6.862-30.639-34.26-53.61-66.938-53.61-13.879 0-26.8 4.155-37.609 11.269v-37.358c12.314-5.092 25.384-7.671 38.971-7.671zm34.215 87.37h-71.153c5.866-13.861 19.604-23.61 35.576-23.61 15.973-.001 29.711 9.749 35.577 23.61zm-103.186 0h-35.569v-190.006h35.569zm-101.138-190.006h35.568v190.006h-35.568zm222.814 378.078h-90.224c-56.563 0-105.897-31.527-131.367-77.928h352.958c-25.469 46.401-74.804 77.928-131.367 77.928zm149.746-149.746c0 14.51-2.082 28.541-5.95 41.818h-377.816c-3.868-13.278-5.95-27.309-5.95-41.818v-8.326h389.716z"/>
        </Svg>
    )
}

export default NoodleIcon

const styles = StyleSheet.create({})
