"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecipeService = void 0;
const tslib_1 = require("tslib");
const mongoose_1 = tslib_1.__importDefault(require("mongoose"));
const schemas_1 = require("../database/schemas");
const CacheService_1 = require("./CacheService");
class RecipeService {
    constructor() {
        this.cacheService = new CacheService_1.CacheService();
    }
    /**
     * Generate a recipe from selected fridge items
     */
    generateRecipe(userId_1, fridgeItemIds_1) {
        return tslib_1.__awaiter(this, arguments, void 0, function* (userId, fridgeItemIds, userTier = 'FREE') {
            try {
                // Check daily generation limits
                const limitCheck = yield this.checkDailyLimit(userId, userTier);
                if (!limitCheck.success) {
                    return limitCheck;
                }
                // Get fridge items
                const fridgeItems = yield schemas_1.FridgeItem.find({
                    _id: { $in: fridgeItemIds.map(id => new mongoose_1.default.Types.ObjectId(id)) },
                    userId: new mongoose_1.default.Types.ObjectId(userId)
                }).lean();
                if (fridgeItems.length === 0) {
                    return {
                        success: false,
                        error: 'No valid fridge items found',
                        statusCode: 400
                    };
                }
                // Create ingredient hash for duplicate detection
                const ingredientNames = fridgeItems.map(item => item.name.toLowerCase().trim()).sort();
                const ingredientHash = require('crypto')
                    .createHash('md5')
                    .update(ingredientNames.join('|'))
                    .digest('hex');
                // First check cache for existing recipe
                let cachedRecipe = yield this.cacheService.getCachedRecipe(ingredientHash);
                if (cachedRecipe) {
                    console.log(`Recipe found in cache for hash: ${ingredientHash}`);
                    return {
                        success: true,
                        data: {
                            recipe: cachedRecipe,
                            isFromCache: true,
                            usageInfo: yield this.getUserUsageInfo(userId, userTier)
                        }
                    };
                }
                // Search for existing recipe in database
                let existingRecipe = yield schemas_1.Recipe.findOne({
                    ingredientHash,
                    $or: [
                        { userId: new mongoose_1.default.Types.ObjectId(userId) },
                        { userId: null } // System recipes
                    ]
                }).lean();
                if (existingRecipe) {
                    const formattedRecipe = this.formatRecipeResponse(existingRecipe);
                    // Cache the recipe for future use
                    yield this.cacheService.cacheRecipe(ingredientHash, formattedRecipe, 24 * 60 * 60); // 24 hours
                    // Return existing recipe without incrementing usage
                    return {
                        success: true,
                        data: {
                            recipe: formattedRecipe,
                            isFromCache: true,
                            usageInfo: yield this.getUserUsageInfo(userId, userTier)
                        }
                    };
                }
                // Generate new recipe using AI service
                const aiRequest = {
                    ingredients: ingredientNames
                };
                const aiRecipe = yield this.callAIService(aiRequest);
                if (!aiRecipe) {
                    return {
                        success: false,
                        error: 'Failed to generate recipe from AI service',
                        statusCode: 500
                    };
                }
                // Create and save new recipe
                const newRecipe = new schemas_1.Recipe({
                    title: aiRecipe.title,
                    ingredients: aiRecipe.ingredients,
                    steps: aiRecipe.steps,
                    userId: new mongoose_1.default.Types.ObjectId(userId),
                    source: 'AI',
                    isLiked: false,
                    ingredientHash
                });
                const savedRecipe = yield newRecipe.save();
                const formattedRecipe = this.formatRecipeResponse(savedRecipe);
                // Cache the newly generated recipe
                yield this.cacheService.cacheRecipe(ingredientHash, formattedRecipe, 24 * 60 * 60); // 24 hours
                // Increment user's daily usage counter
                yield schemas_1.UserRecipeUsage.incrementUsage(new mongoose_1.default.Types.ObjectId(userId), userTier);
                return {
                    success: true,
                    data: {
                        recipe: formattedRecipe,
                        isFromCache: false,
                        usageInfo: yield this.getUserUsageInfo(userId, userTier)
                    }
                };
            }
            catch (error) {
                return {
                    success: false,
                    error: error.message,
                    statusCode: 500
                };
            }
        });
    }
    /**
     * Check if user has reached daily generation limit
     */
    checkDailyLimit(userId, userTier) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                const usage = yield schemas_1.UserRecipeUsage.getOrCreateTodayUsage(new mongoose_1.default.Types.ObjectId(userId), userTier);
                const dailyLimit = userTier === 'PRO' ? 15 : 5;
                if (usage.generatedCount >= dailyLimit) {
                    const tomorrow = new Date();
                    tomorrow.setDate(tomorrow.getDate() + 1);
                    tomorrow.setHours(0, 0, 0, 0);
                    return {
                        success: false,
                        error: 'Daily recipe generation limit reached',
                        statusCode: 429,
                        data: {
                            dailyUsed: usage.generatedCount,
                            dailyLimit,
                            resetTime: tomorrow.toISOString()
                        }
                    };
                }
                return { success: true };
            }
            catch (error) {
                return {
                    success: false,
                    error: error.message,
                    statusCode: 500
                };
            }
        });
    }
    /**
     * Get user's current usage information
     */
    getUserUsageInfo(userId, userTier) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            const usage = yield schemas_1.UserRecipeUsage.getOrCreateTodayUsage(new mongoose_1.default.Types.ObjectId(userId), userTier);
            const dailyLimit = userTier === 'PRO' ? 15 : 5;
            return {
                dailyUsed: usage.generatedCount,
                dailyLimit,
                remainingGenerations: Math.max(0, dailyLimit - usage.generatedCount)
            };
        });
    }
    /**
     * Toggle like status of a recipe
     */
    likeRecipe(userId, recipeId) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                const recipe = yield schemas_1.Recipe.findOne({
                    _id: new mongoose_1.default.Types.ObjectId(recipeId),
                    $or: [
                        { userId: new mongoose_1.default.Types.ObjectId(userId) },
                        { userId: null } // System recipes can be liked by anyone
                    ]
                });
                if (!recipe) {
                    return {
                        success: false,
                        error: 'Recipe not found or access denied',
                        statusCode: 404
                    };
                }
                recipe.isLiked = !recipe.isLiked;
                const updatedRecipe = yield recipe.save();
                return {
                    success: true,
                    data: this.formatRecipeResponse(updatedRecipe)
                };
            }
            catch (error) {
                return {
                    success: false,
                    error: error.message,
                    statusCode: 500
                };
            }
        });
    }
    /**
     * Get user's recipes with filtering
     */
    getUserRecipes(userId_1) {
        return tslib_1.__awaiter(this, arguments, void 0, function* (userId, queryParams = {}) {
            try {
                const { page = 1, limit = 20, sortBy = 'createdAt', sortOrder = 'desc', source, isLiked, search, dateFrom, dateTo } = queryParams;
                // Build filter query
                const filter = {
                    $or: [
                        { userId: new mongoose_1.default.Types.ObjectId(userId) },
                        { userId: null } // Include system recipes
                    ]
                };
                if (source) {
                    filter.source = source;
                }
                if (isLiked !== undefined) {
                    filter.isLiked = isLiked;
                }
                if (search) {
                    // Preserve existing $or conditions and extend with search conditions
                    const searchConditions = [
                        { title: { $regex: search, $options: 'i' } },
                        { 'ingredients.name': { $regex: search, $options: 'i' } }
                    ];
                    if (filter.$or) {
                        // If $or already exists, combine with AND logic:
                        // (existing $or conditions) AND (search conditions)
                        filter.$and = [
                            { $or: filter.$or },
                            { $or: searchConditions }
                        ];
                        delete filter.$or;
                    }
                    else {
                        // If no existing $or, just set the search conditions
                        filter.$or = searchConditions;
                    }
                }
                if (dateFrom || dateTo) {
                    filter.createdAt = {};
                    if (dateFrom)
                        filter.createdAt.$gte = new Date(dateFrom);
                    if (dateTo)
                        filter.createdAt.$lte = new Date(dateTo);
                }
                // Build sort object
                const sort = {};
                sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
                // Calculate pagination
                const skip = (page - 1) * limit;
                // Execute queries
                const [recipes, total] = yield Promise.all([
                    schemas_1.Recipe.find(filter)
                        .sort(sort)
                        .skip(skip)
                        .limit(limit)
                        .lean(),
                    schemas_1.Recipe.countDocuments(filter)
                ]);
                // Calculate pagination metadata
                const totalPages = Math.ceil(total / limit);
                const pagination = {
                    page,
                    limit,
                    total,
                    totalPages,
                    hasNextPage: page < totalPages,
                    hasPrevPage: page > 1
                };
                return {
                    success: true,
                    data: {
                        recipes: recipes.map(recipe => this.formatRecipeResponse(recipe)),
                        pagination
                    }
                };
            }
            catch (error) {
                return {
                    success: false,
                    error: error.message,
                    statusCode: 500
                };
            }
        });
    }
    /**
     * Get daily limit information for user
     */
    getDailyLimitInfo(userId, userTier) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                const usage = yield schemas_1.UserRecipeUsage.getOrCreateTodayUsage(new mongoose_1.default.Types.ObjectId(userId), userTier);
                const dailyLimit = userTier === 'PRO' ? 15 : 5;
                const tomorrow = new Date();
                tomorrow.setDate(tomorrow.getDate() + 1);
                tomorrow.setHours(0, 0, 0, 0);
                return {
                    success: true,
                    data: {
                        dailyUsed: usage.generatedCount,
                        dailyLimit,
                        remainingGenerations: Math.max(0, dailyLimit - usage.generatedCount),
                        usagePercentage: Math.min(100, (usage.generatedCount / dailyLimit) * 100),
                        canGenerateMore: usage.generatedCount < dailyLimit,
                        userTier,
                        resetTime: tomorrow.toISOString()
                    }
                };
            }
            catch (error) {
                return {
                    success: false,
                    error: error.message,
                    statusCode: 500
                };
            }
        });
    }
    /**
     * Call AI service to generate recipe (placeholder implementation)
     */
    callAIService(request) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                // This is a placeholder implementation
                // In a real application, you would call an actual AI service like OpenAI, Claude, etc.
                // For now, return a mock recipe
                const mockRecipe = {
                    title: `Delicious ${request.ingredients.join(' and ')} Recipe`,
                    ingredients: request.ingredients.map((ingredient, index) => ({
                        name: ingredient,
                        quantity: Math.floor(Math.random() * 3) + 1,
                        unit: ['cups', 'pieces', 'tbsp'][Math.floor(Math.random() * 3)]
                    })),
                    steps: [
                        `Prepare all ingredients: ${request.ingredients.join(', ')}.`,
                        'Heat a pan over medium heat.',
                        'Add the ingredients to the pan and cook for 10-15 minutes.',
                        'Season with salt and pepper to taste.',
                        'Serve hot and enjoy!'
                    ],
                    estimatedCookingTime: 30,
                    servings: 4,
                    difficulty: 'easy'
                };
                return mockRecipe;
            }
            catch (error) {
                console.error('AI service call failed:', error);
                return null;
            }
        });
    }
    /**
     * Format recipe for API response
     */
    formatRecipeResponse(recipe) {
        var _a, _b, _c;
        return {
            id: recipe._id.toString(),
            title: recipe.title,
            ingredients: recipe.ingredients,
            steps: recipe.steps,
            userId: (_a = recipe.userId) === null || _a === void 0 ? void 0 : _a.toString(),
            source: recipe.source,
            isLiked: recipe.isLiked,
            ingredientHash: recipe.ingredientHash,
            createdAt: recipe.createdAt.toISOString(),
            updatedAt: recipe.updatedAt.toISOString(),
            ingredientCount: ((_b = recipe.ingredients) === null || _b === void 0 ? void 0 : _b.length) || 0,
            stepCount: ((_c = recipe.steps) === null || _c === void 0 ? void 0 : _c.length) || 0
        };
    }
}
exports.RecipeService = RecipeService;
