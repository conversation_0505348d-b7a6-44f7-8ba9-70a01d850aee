"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getRecommendations = exports.getFavoriteItems = exports.toggleFavorite = exports.deleteFridgeItem = exports.updateFridgeItem = exports.createFridgeItem = exports.getFridgeItems = void 0;
const tslib_1 = require("tslib");
const services_1 = require("../services");
const fridgeService = new services_1.FridgeService();
/**
 * Get all fridge items for authenticated user
 * GET /fridge
 */
const getFridgeItems = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    const authReq = req;
    try {
        if (!authReq.user) {
            res.status(401).json({ success: false, message: 'Unauthorized', timestamp: new Date().toISOString() });
            return;
        }
        const userId = authReq.user._id;
        const queryParams = {
            page: parseInt(req.query.page) || 1,
            limit: parseInt(req.query.limit) || 20,
            sortBy: req.query.sortBy || 'expiryDate',
            sortOrder: req.query.sortOrder || 'asc',
            expiryStatus: req.query.expiryStatus,
            isFavorite: req.query.isFavorite ? req.query.isFavorite === 'true' : undefined,
            search: req.query.search
        };
        const result = yield fridgeService.getUserFridgeItems(userId, queryParams);
        if (!result.success) {
            res.status(result.statusCode || 500).json({
                success: false,
                message: result.error,
                timestamp: new Date().toISOString()
            });
            return;
        }
        if (!result.data) {
            res.status(500).json({ success: false, message: 'Missing data', timestamp: new Date().toISOString() });
            return;
        }
        res.status(200).json({
            success: true,
            data: result.data.items,
            pagination: result.data.pagination, timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            timestamp: new Date().toISOString()
        });
    }
});
exports.getFridgeItems = getFridgeItems;
/**
 * Create a new fridge item
 * POST /fridge
 */
const createFridgeItem = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const authReq = req;
        if (!authReq.user) {
            res.status(401).json({ success: false, message: 'Unauthorized', timestamp: new Date().toISOString() });
            return;
        }
        const userId = authReq.user._id;
        const itemData = req.body;
        // Basic validation
        if (!itemData.name || !itemData.quantity || !itemData.unit || !itemData.expiryDate) {
            res.status(400).json({
                success: false,
                message: 'Missing required fields: name, quantity, unit, expiryDate',
                timestamp: new Date().toISOString()
            });
            return;
        }
        const result = yield fridgeService.createFridgeItem(userId, itemData);
        if (!result.success) {
            res.status(result.statusCode || 500).json({
                success: false,
                message: result.error,
                timestamp: new Date().toISOString()
            });
            return;
        }
        res.status(201).json({
            success: true,
            data: result.data,
            message: 'Fridge item created successfully',
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            timestamp: new Date().toISOString()
        });
    }
});
exports.createFridgeItem = createFridgeItem;
/**
 * Update an existing fridge item
 * PUT /fridge/:id
 */
const updateFridgeItem = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const authReq = req;
        if (!authReq.user) {
            res.status(401).json({ success: false, message: 'Unauthorized', timestamp: new Date().toISOString() });
            return;
        }
        const userId = authReq.user._id;
        const itemId = req.params.id;
        const updateData = req.body;
        if (!itemId) {
            res.status(400).json({
                success: false,
                message: 'Item ID is required',
                timestamp: new Date().toISOString()
            });
            return;
        }
        const result = yield fridgeService.updateFridgeItem(userId, itemId, updateData);
        if (!result.success) {
            res.status(result.statusCode || 500).json({
                success: false,
                message: result.error,
                timestamp: new Date().toISOString()
            });
            return;
        }
        res.status(200).json({
            success: true,
            data: result.data,
            message: 'Fridge item updated successfully',
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            timestamp: new Date().toISOString()
        });
    }
});
exports.updateFridgeItem = updateFridgeItem;
/**
 * Delete a fridge item
 * DELETE /fridge/:id
 */
const deleteFridgeItem = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const authReq = req;
        if (!authReq.user) {
            res.status(401).json({ success: false, message: 'Unauthorized', timestamp: new Date().toISOString() });
            return;
        }
        const userId = authReq.user._id;
        const itemId = req.params.id;
        if (!itemId) {
            res.status(400).json({
                success: false,
                message: 'Item ID is required',
                timestamp: new Date().toISOString()
            });
            return;
        }
        const result = yield fridgeService.deleteFridgeItem(userId, itemId);
        if (!result.success) {
            res.status(result.statusCode || 500).json({
                success: false,
                message: result.error,
                timestamp: new Date().toISOString()
            });
            return;
        }
        res.status(200).json({
            success: true,
            message: 'Fridge item deleted successfully',
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            timestamp: new Date().toISOString()
        });
    }
});
exports.deleteFridgeItem = deleteFridgeItem;
/**
 * Toggle favorite status of a fridge item
 * POST /fridge/:id/favorite
 */
const toggleFavorite = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const authReq = req;
        if (!authReq.user) {
            res.status(401).json({ success: false, message: 'Unauthorized', timestamp: new Date().toISOString() });
            return;
        }
        const userId = authReq.user._id;
        const itemId = req.params.id;
        if (!itemId) {
            res.status(400).json({
                success: false,
                message: 'Item ID is required',
                timestamp: new Date().toISOString()
            });
            return;
        }
        const result = yield fridgeService.toggleFavoriteStatus(userId, itemId);
        if (!result.success) {
            res.status(result.statusCode || 500).json({
                success: false,
                message: result.error,
                timestamp: new Date().toISOString()
            });
            return;
        }
        res.status(200).json({
            success: true,
            data: result.data,
            message: 'Favorite status updated successfully',
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            timestamp: new Date().toISOString()
        });
    }
});
exports.toggleFavorite = toggleFavorite;
/**
 * Get favorite items for authenticated user
 * GET /fridge/favorites
 */
const getFavoriteItems = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const authReq = req;
        if (!authReq.user) {
            res.status(401).json({ success: false, message: 'Unauthorized', timestamp: new Date().toISOString() });
            return;
        }
        const userId = authReq.user._id;
        const result = yield fridgeService.getFavoriteItems(userId);
        if (!result.success) {
            res.status(result.statusCode || 500).json({
                success: false,
                message: result.error,
                timestamp: new Date().toISOString()
            });
            return;
        }
        res.status(200).json({
            success: true,
            data: result.data,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            timestamp: new Date().toISOString()
        });
    }
});
exports.getFavoriteItems = getFavoriteItems;
/**
 * Get recommended items for authenticated user
 * GET /fridge/recommendations
 */
const getRecommendations = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const authReq = req;
        if (!authReq.user) {
            res.status(401).json({ success: false, message: 'Unauthorized', timestamp: new Date().toISOString() });
            return;
        }
        const userId = authReq.user._id;
        const result = yield fridgeService.getRecommendedItems(userId);
        if (!result.success) {
            res.status(result.statusCode || 500).json({
                success: false,
                message: result.error,
                timestamp: new Date().toISOString()
            });
            return;
        }
        res.status(200).json({
            success: true,
            data: result.data,
            message: 'Recommendations retrieved successfully',
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            timestamp: new Date().toISOString()
        });
    }
});
exports.getRecommendations = getRecommendations;
