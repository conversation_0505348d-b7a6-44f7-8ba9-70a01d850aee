"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCronJobStatus = exports.stopCronJobs = exports.initializeCronJobs = void 0;
const tslib_1 = require("tslib");
const node_cron_1 = tslib_1.__importDefault(require("node-cron"));
const queue_1 = require("./queue");
const recipeGenerationProcessor_1 = require("./processors/recipeGenerationProcessor");
/**
 * CRON job configurations
 */
const cronJobs = {
    // Daily recipe generation at 6:00 AM
    dailyRecipeGeneration: {
        schedule: '0 6 * * *', // Every day at 6:00 AM
        timezone: 'UTC',
        enabled: true,
        name: 'Daily Recipe Generation'
    },
    // Cleanup old jobs at 2:00 AM
    cleanup: {
        schedule: '0 2 * * *', // Every day at 2:00 AM
        timezone: 'UTC',
        enabled: true,
        name: 'Job Cleanup'
    },
    // Health check every 30 minutes
    healthCheck: {
        schedule: '*/30 * * * *', // Every 30 minutes
        timezone: 'UTC',
        enabled: true,
        name: 'Health Check'
    }
};
/**
 * Initialize and start all CRON jobs
 */
const initializeCronJobs = () => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    console.log('Initializing CRON jobs...');
    // Daily recipe generation job
    if (cronJobs.dailyRecipeGeneration.enabled) {
        node_cron_1.default.schedule(cronJobs.dailyRecipeGeneration.schedule, () => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
            console.log(`[CRON] Starting ${cronJobs.dailyRecipeGeneration.name}...`);
            try {
                yield (0, recipeGenerationProcessor_1.queueDailyRecipeGeneration)();
                console.log(`[CRON] ${cronJobs.dailyRecipeGeneration.name} completed successfully`);
            }
            catch (error) {
                console.error(`[CRON] ${cronJobs.dailyRecipeGeneration.name} failed:`, error.message);
            }
        }), {
            timezone: cronJobs.dailyRecipeGeneration.timezone
        });
        console.log(`✓ ${cronJobs.dailyRecipeGeneration.name} scheduled: ${cronJobs.dailyRecipeGeneration.schedule}`);
    }
    // Cleanup job
    if (cronJobs.cleanup.enabled) {
        node_cron_1.default.schedule(cronJobs.cleanup.schedule, () => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
            console.log(`[CRON] Starting ${cronJobs.cleanup.name}...`);
            try {
                yield performCleanup();
                console.log(`[CRON] ${cronJobs.cleanup.name} completed successfully`);
            }
            catch (error) {
                console.error(`[CRON] ${cronJobs.cleanup.name} failed:`, error.message);
            }
        }), {
            timezone: cronJobs.cleanup.timezone
        });
        console.log(`✓ ${cronJobs.cleanup.name} scheduled: ${cronJobs.cleanup.schedule}`);
    }
    // Health check job
    if (cronJobs.healthCheck.enabled) {
        node_cron_1.default.schedule(cronJobs.healthCheck.schedule, () => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
            try {
                yield performHealthCheck();
            }
            catch (error) {
                console.error(`[CRON] ${cronJobs.healthCheck.name} failed:`, error.message);
            }
        }), {
            timezone: cronJobs.healthCheck.timezone
        });
        console.log(`✓ ${cronJobs.healthCheck.name} scheduled: ${cronJobs.healthCheck.schedule}`);
    }
    console.log('All CRON jobs initialized successfully');
});
exports.initializeCronJobs = initializeCronJobs;
/**
 * Perform cleanup of old jobs and data
 */
function performCleanup() {
    return tslib_1.__awaiter(this, void 0, void 0, function* () {
        try {
            // Clean up completed jobs older than 24 hours
            const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
            // Clean recipe generation queue
            yield queue_1.recipeGenerationQueue.clean(oneDayAgo, 'completed');
            yield queue_1.recipeGenerationQueue.clean(oneDayAgo, 'failed');
            console.log('Job queues cleaned successfully');
            // Add cleanup job to queue for database cleanup
            yield queue_1.cleanupQueue.add('database-cleanup', {
                timestamp: new Date().toISOString()
            }, {
                attempts: 2,
                backoff: {
                    type: 'exponential',
                    delay: 5000
                }
            });
        }
        catch (error) {
            console.error('Cleanup failed:', error.message);
            throw error;
        }
    });
}
/**
 * Perform health check on job queues
 */
function performHealthCheck() {
    return tslib_1.__awaiter(this, void 0, void 0, function* () {
        try {
            // Check queue health
            const recipeQueueHealth = yield queue_1.recipeGenerationQueue.getJobCounts();
            // Log queue statistics
            console.log('[HEALTH] Recipe Generation Queue:', {
                waiting: recipeQueueHealth.waiting,
                active: recipeQueueHealth.active,
                completed: recipeQueueHealth.completed,
                failed: recipeQueueHealth.failed
            });
            // Alert if too many failed jobs
            if (recipeQueueHealth.failed > 50) {
                console.warn(`[HEALTH] High number of failed jobs in recipe generation queue: ${recipeQueueHealth.failed}`);
            }
            // Alert if queue is stalled
            if (recipeQueueHealth.active > 0 && recipeQueueHealth.waiting > 100) {
                console.warn(`[HEALTH] Recipe generation queue may be stalled. Active: ${recipeQueueHealth.active}, Waiting: ${recipeQueueHealth.waiting}`);
            }
        }
        catch (error) {
            console.error('Health check failed:', error.message);
        }
    });
}
/**
 * Stop all CRON jobs (for graceful shutdown)
 */
const stopCronJobs = () => {
    console.log('Stopping all CRON jobs...');
    node_cron_1.default.getTasks().forEach((task) => {
        task.stop();
    });
    console.log('All CRON jobs stopped');
};
exports.stopCronJobs = stopCronJobs;
/**
 * Get status of all CRON jobs
 */
const getCronJobStatus = () => {
    const tasks = node_cron_1.default.getTasks();
    const status = Object.entries(cronJobs).map(([key, config]) => ({
        name: config.name,
        schedule: config.schedule,
        enabled: config.enabled,
        timezone: config.timezone,
        running: tasks.size > 0
    }));
    return {
        totalJobs: Object.keys(cronJobs).length,
        enabledJobs: Object.values(cronJobs).filter(job => job.enabled).length,
        jobs: status
    };
};
exports.getCronJobStatus = getCronJobStatus;
// Handle graceful shutdown
process.on('SIGTERM', () => {
    (0, exports.stopCronJobs)();
    process.exit(0);
});
process.on('SIGINT', () => {
    (0, exports.stopCronJobs)();
    process.exit(0);
});
