"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheService = void 0;
const tslib_1 = require("tslib");
const queue_1 = require("../jobs/queue");
class CacheService {
    constructor() {
        this.defaultTTL = 24 * 60 * 60; // 24 hours in seconds
    }
    /**
     * Cache a generated recipe
     */
    cacheRecipe(ingredientHash, recipe, ttl) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                const cacheKey = `recipe:${ingredientHash}`;
                const cacheData = {
                    recipe,
                    cachedAt: new Date(),
                    expiresAt: new Date(Date.now() + (ttl || this.defaultTTL) * 1000)
                };
                yield queue_1.redis.setex(cacheKey, ttl || this.defaultTTL, JSON.stringify(cacheData));
                console.log(`Recipe cached with key: ${cacheKey}`);
                return true;
            }
            catch (error) {
                console.error('Failed to cache recipe:', error.message);
                return false;
            }
        });
    }
    /**
     * Get cached recipe by ingredient hash
     */
    getCachedRecipe(ingredientHash) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                const cacheKey = `recipe:${ingredientHash}`;
                const cachedData = yield queue_1.redis.get(cacheKey);
                if (!cachedData) {
                    return null;
                }
                const parsedData = JSON.parse(cachedData);
                // Check if cache has expired (double check)
                if (new Date() > new Date(parsedData.expiresAt)) {
                    yield this.deleteCachedRecipe(ingredientHash);
                    return null;
                }
                console.log(`Recipe retrieved from cache: ${cacheKey}`);
                return parsedData.recipe;
            }
            catch (error) {
                console.error('Failed to get cached recipe:', error.message);
                return null;
            }
        });
    }
    /**
     * Delete cached recipe
     */
    deleteCachedRecipe(ingredientHash) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                const cacheKey = `recipe:${ingredientHash}`;
                const result = yield queue_1.redis.del(cacheKey);
                return result > 0;
            }
            catch (error) {
                console.error('Failed to delete cached recipe:', error.message);
                return false;
            }
        });
    }
    /**
     * Cache user daily limits
     */
    cacheUserLimit(userId, date, limitData, ttl) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                const cacheKey = `user_limit:${userId}:${date}`;
                const cacheExpiry = ttl || (24 * 60 * 60); // 24 hours
                yield queue_1.redis.setex(cacheKey, cacheExpiry, JSON.stringify(limitData));
                console.log('User limit cached', { userId: userId.slice(0, 6) + '…', date });
                return true;
            }
            catch (error) {
                console.error('Failed to cache user limit:', error.message);
                return false;
            }
        });
    }
    /**
     * Get cached user daily limits
     */
    getCachedUserLimit(userId, date) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                const cacheKey = `user_limit:${userId}:${date}`;
                const cachedData = yield queue_1.redis.get(cacheKey);
                if (!cachedData) {
                    return null;
                }
                return JSON.parse(cachedData);
            }
            catch (error) {
                console.error('Failed to get cached user limit:', error.message);
                return null;
            }
        });
    }
    /**
     * Cache fridge items for a user
     */
    cacheFridgeItems(userId, items, ttl) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                const cacheKey = `fridge_items:${userId}`;
                const cacheExpiry = ttl || (30 * 60); // 30 minutes
                const cacheData = {
                    items,
                    cachedAt: new Date(),
                    expiresAt: new Date(Date.now() + cacheExpiry * 1000)
                };
                yield queue_1.redis.setex(cacheKey, cacheExpiry, JSON.stringify(cacheData));
                return true;
            }
            catch (error) {
                console.error('Failed to cache fridge items:', error.message);
                return false;
            }
        });
    }
    /**
     * Get cached fridge items
     */
    getCachedFridgeItems(userId) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                const cacheKey = `fridge_items:${userId}`;
                const cachedData = yield queue_1.redis.get(cacheKey);
                if (!cachedData) {
                    return null;
                }
                const parsedData = JSON.parse(cachedData);
                // Check if cache has expired
                if (new Date() > new Date(parsedData.expiresAt)) {
                    yield queue_1.redis.del(cacheKey);
                    return null;
                }
                return parsedData.items;
            }
            catch (error) {
                console.error('Failed to get cached fridge items:', error.message);
                return null;
            }
        });
    }
    /**
     * Invalidate fridge items cache for a user
     */
    invalidateFridgeItemsCache(userId) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                const cacheKey = `fridge_items:${userId}`;
                const result = yield queue_1.redis.del(cacheKey);
                return result > 0;
            }
            catch (error) {
                console.error('Failed to invalidate fridge items cache:', error.message);
                return false;
            }
        });
    }
    /**
     * Cache user recommendations
     */
    cacheRecommendations(userId, recommendations, ttl) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                const cacheKey = `recommendations:${userId}`;
                const cacheExpiry = ttl || (60 * 60); // 1 hour
                yield queue_1.redis.setex(cacheKey, cacheExpiry, JSON.stringify(recommendations));
                return true;
            }
            catch (error) {
                console.error('Failed to cache recommendations:', error.message);
                return false;
            }
        });
    }
    /**
     * Get cached recommendations
     */
    getCachedRecommendations(userId) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                const cacheKey = `recommendations:${userId}`;
                const cachedData = yield queue_1.redis.get(cacheKey);
                if (!cachedData) {
                    return null;
                }
                return JSON.parse(cachedData);
            }
            catch (error) {
                console.error('Failed to get cached recommendations:', error.message);
                return null;
            }
        });
    }
    /**
     * Generic cache set method
     */
    set(key, value, ttl) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                const serializedValue = JSON.stringify(value);
                if (ttl) {
                    yield queue_1.redis.setex(key, ttl, serializedValue);
                }
                else {
                    yield queue_1.redis.set(key, serializedValue);
                }
                return true;
            }
            catch (error) {
                console.error(`Failed to cache key ${key}:`, error.message);
                return false;
            }
        });
    }
    /**
     * Generic cache get method
     */
    get(key) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                const cachedData = yield queue_1.redis.get(key);
                if (!cachedData) {
                    return null;
                }
                return JSON.parse(cachedData);
            }
            catch (error) {
                console.error(`Failed to get cached key ${key}:`, error.message);
                return null;
            }
        });
    }
    /**
     * Delete cache key
     */
    delete(key) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield queue_1.redis.del(key);
                return result > 0;
            }
            catch (error) {
                console.error(`Failed to delete cache key ${key}:`, error.message);
                return false;
            }
        });
    }
    /**
     * Check if cache key exists
     */
    exists(key) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield queue_1.redis.exists(key);
                return result === 1;
            }
            catch (error) {
                console.error(`Failed to check cache key ${key}:`, error.message);
                return false;
            }
        });
    }
    /**
     * Set cache expiration
     */
    expire(key, ttl) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                const result = yield queue_1.redis.expire(key, ttl);
                return result === 1;
            }
            catch (error) {
                console.error(`Failed to set expiration for key ${key}:`, error.message);
                return false;
            }
        });
    }
    /**
     * Get cache statistics
     */
    getStats() {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                const info = yield queue_1.redis.info('memory');
                const keyspace = yield queue_1.redis.info('keyspace');
                return {
                    memory: info,
                    keyspace: keyspace,
                    timestamp: new Date().toISOString()
                };
            }
            catch (error) {
                console.error('Failed to get cache stats:', error.message);
                return null;
            }
        });
    }
    /**
     * Clear cache with safety checks and prefix-based deletion
     * Only works in test environments or when explicitly allowed
     */
    clearAll(prefix) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                // Environment safety check
                const nodeEnv = process.env.NODE_ENV;
                const allowCacheFlush = process.env.ALLOW_CACHE_FLUSH === 'true';
                const isTestEnv = nodeEnv === 'test' || nodeEnv === 'development';
                if (!isTestEnv && !allowCacheFlush) {
                    const errorMsg = `Cache flush denied: NODE_ENV=${nodeEnv}, ALLOW_CACHE_FLUSH=${process.env.ALLOW_CACHE_FLUSH}. Only allowed in test/development environments or when ALLOW_CACHE_FLUSH=true`;
                    console.error(errorMsg);
                    throw new Error(errorMsg);
                }
                // Use prefix-scoped deletion instead of global flush
                const searchPrefix = prefix || 'recipe:*'; // Default to recipe prefix
                console.log(`Starting safe cache clear with prefix: ${searchPrefix}`);
                let cursor = '0';
                let deletedCount = 0;
                const batchSize = 100;
                do {
                    // Use SCAN to find keys matching the prefix
                    const result = yield queue_1.redis.scan(cursor, 'MATCH', searchPrefix, 'COUNT', batchSize);
                    cursor = result[0];
                    const keys = result[1];
                    if (keys.length > 0) {
                        // Delete keys in batch using pipeline for better performance
                        const pipeline = queue_1.redis.pipeline();
                        keys.forEach(key => pipeline.unlink(key)); // UNLINK is non-blocking version of DEL
                        yield pipeline.exec();
                        deletedCount += keys.length;
                        console.log(`Deleted ${keys.length} keys (total: ${deletedCount})`);
                    }
                } while (cursor !== '0');
                console.log(`Safe cache clear completed. Deleted ${deletedCount} keys with prefix: ${searchPrefix}`);
                return true;
            }
            catch (error) {
                const errorContext = {
                    method: 'clearAll',
                    prefix: prefix || 'recipe:*',
                    nodeEnv: process.env.NODE_ENV,
                    allowCacheFlush: process.env.ALLOW_CACHE_FLUSH,
                    error: error.message
                };
                console.error('Failed to clear cache safely:', errorContext);
                return false;
            }
        });
    }
}
exports.CacheService = CacheService;
