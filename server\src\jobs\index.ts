// Export queue configurations
export {
  recipeGenerationQueue,
  notificationQueue,
  cleanupQueue,
  shelfLifeQueue,
  redis
} from './queue';

// Export job processors
export {
  initializeJobProcessors,
  queueManualRecipeGeneration,
  queuePushNotification,
  getJobStatus,
  getQueueStats,
  processRecipeGeneration,
  queueDailyRecipeGeneration,
  processPushNotification,
  processShelfLifeLookup,
  processBatchShelfLifeLookup,
  queueUnknownFoodsProcessing,
  queueShelfLifeLookup
} from './processors';

// Export scheduler functions
export {
  initializeCronJobs,
  stopCronJobs,
  getCronJobStatus
} from './scheduler';

// Initialize everything
export const initializeJobSystem = async () => {
  try {
    console.log('Initializing job system...');
    
    // Initialize job processors first
    const { initializeJobProcessors } = await import('./processors');
    await initializeJobProcessors();
    
    // Then initialize CRON jobs
    const { initializeCronJobs } = await import('./scheduler');
    await initializeCronJobs();
    
    console.log('Job system initialized successfully');
    return { success: true };
    
  } catch (error: any) {
    console.error('Failed to initialize job system:', error.message);
    throw error;
  }
};
