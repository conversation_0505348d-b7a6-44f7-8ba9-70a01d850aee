
import { ChatMessage } from '@/types/chat';
import React, { createContext, useState, useEffect, useContext } from 'react';



interface ChatContextProps {
  handleSendMessage: (message: string) => Promise<void>
  handleFetchMessages: (chatId: string) => Promise<void>
  handleNewChat: () => Promise<void>
  messages: ChatMessage[]
}

export const ChatContext = createContext<ChatContextProps>({
  handleSendMessage: async (_message: string) => { },
  handleFetchMessages: async (_chatId: string) => { },
  handleNewChat: async () => { },
  messages: []
});

export const useChat = () => {
  return useContext(ChatContext) as ChatContextProps
}



export const ChatProvider = ({ children }: { children: React.ReactNode }) => {
  const [messages, setMessages] = useState<ChatMessage[]>([])




  const handleFetchMessages = async (chatId: string) => {

  }

  const handleSendMessage = async (message: string) => {
    const data: ChatMessage = {
      id: '',
      type: 'user',
      content: message,
      timestamp: new Date()
    }
    console.log(data)

    setMessages(prev => [...prev, data])
  }
  const handleAgentMessage = (content: string, type: ChatMessage["type"] = "bot") => {
    const newMessage: ChatMessage = {
      id: Date.now().toString(),
      type,
      content,
      timestamp: new Date(),
      //ingredients: type === "ingredient-display" ? userIngredients : undefined,
    }
    setMessages((prev) => [...prev, newMessage])

  }

  const handleNewChat = async () => {
    setMessages([])
  }





  return (
    <ChatContext.Provider value={{ handleSendMessage, handleFetchMessages, handleNewChat, messages }}>
      {children}
    </ChatContext.Provider>
  );
};