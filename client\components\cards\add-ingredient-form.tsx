"use client"

import type React from "react"
import { useState } from "react"
import Image from "next/image"
import AlertCard from "../global/alert-card"

export default function AddIngredientForm({
  onAdd,
  onClose,
}: {
  onAdd: (ingredient: string) => void
  onClose: () => void
}) {
  const [ingredient, setIngredient] = useState("")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (ingredient.trim()) {
      onAdd(ingredient.trim())
      setIngredient("")
      onClose()
    }
  }

  return (
    <div className="p-4 w-full max-w-md mx-auto">
       <AlertCard
        title="Expiry Date"
        description="Our AI will help you track the expiry date of your ingredients."
        
        variant="info"
        className="mb-4"
      />
      <Image
        src="/images/leftover-chef_basket.png"
        alt="Fridge"
        width={200}
        height={400}
        className="mx-auto mb-4"
      />

      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-medium text-gray-700">Add Ingredient</h2>
        {/* <button
          onClick={onClose}
          className="text-gray-500 hover:text-gray-700 border-none bg-transparent p-1"
          type="button"
        >
          <X className="h-5 w-5" />
          <span className="sr-only">Close</span>
        </button> */}
      </div>

      <form onSubmit={handleSubmit}>
        <div className="mb-4 flex flex-col gap-2 text-gray-700">
          <input
            type="text"
            placeholder="Enter ingredient name"
            value={ingredient}
            onChange={(e) => setIngredient(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            
          />
        </div>

        <button
          type="submit"
          className="w-full bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded-full transition duration-200 ease-in-out"
        >
          Add to Fridge
        </button>
      </form>
    </div>
  )
}
