import { recipeGenerationQueue, notificationQueue, cleanupQueue, shelfLifeQueue } from '../queue';
import {
  processRecipeGeneration,
  queueDailyRecipeGeneration
} from './recipeGenerationProcessor';
import {
  processPushNotification
} from './notificationProcessor';
import {
  processShelfLifeLookup,
  processBatchShelfLifeLookup,
  queueUnknownFoodsProcessing
} from './shelfLifeProcessor';
import { UserRecipeUsage } from '../../database/schemas';

// Module-scoped flag to ensure processors are only initialized once
let processorsInitialized = false;

/**
 * Initialize all job processors
 */
export const initializeJobProcessors = async () => {
  // Return early if processors have already been initialized
  if (processorsInitialized) {
    console.log('Job processors already initialized, skipping...');
    return;
  }

  console.log('Initializing job processors...');

  // Recipe generation processor
  recipeGenerationQueue.process('generate-recipe', 5, processRecipeGeneration);

  // Notification processors
  notificationQueue.process('send-push-notification', 10, processPushNotification);

  // Shelf life processors
  shelfLifeQueue.process('shelf-life-lookup', 3, processShelfLifeLookup);
  shelfLifeQueue.process('batch-shelf-life-lookup', 1, processBatchShelfLifeLookup);

  // Cleanup processor
  cleanupQueue.process('database-cleanup', 1, processDatabaseCleanup);

  // Mark processors as initialized to prevent duplicate registration
  processorsInitialized = true;

  console.log('All job processors initialized successfully');
};

/**
 * Database cleanup processor
 */
async function processDatabaseCleanup(job: any) {
  try {
    console.log('Starting database cleanup...');

    // Clean up old user recipe usage records (older than 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const deletedUsageRecords = await UserRecipeUsage.deleteMany({
      date: { $lt: thirtyDaysAgo }
    });

    console.log(`Deleted ${deletedUsageRecords.deletedCount} old usage records`);

    return {
      success: true,
      deletedUsageRecords: deletedUsageRecords.deletedCount,
      timestamp: new Date().toISOString()
    };

  } catch (error: any) {
    console.error('Database cleanup failed:', error.message);
    throw error;
  }
}

/**
 * Queue a manual recipe generation job
 */
export const queueManualRecipeGeneration = async (
  userId: string,
  fridgeItemIds: string[],
  userTier: 'FREE' | 'PRO'
) => {
  try {
    const jobPayload = {
      userId,
      expiringItemIds: fridgeItemIds,
      userTier,
      jobType: 'manual' as const
    };

    const job = await recipeGenerationQueue.add('generate-recipe', jobPayload, {
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000
      },
      removeOnComplete: 5,
      removeOnFail: 10,
      // Bull priority: 1 is highest priority
      priority: userTier === 'PRO' ? 1 : 5
    });

    const maskedUserId = `${userId.slice(0, 4)}…${userId.slice(-4)}`;
    console.log(`Queued manual recipe generation job ${job.id} for user ${maskedUserId}`);

    return {
      success: true,
      jobId: job.id,
      estimatedDelay: await getEstimatedProcessingTime()
    };

  } catch (error: any) {
    console.error('Failed to queue manual recipe generation:', error.message);
    throw error;
  }
};

/**
 * Queue a push notification
 */
export const queuePushNotification = async (
  userId: string,
  title: string,
  body: string,
  data?: Record<string, any>,
  delay?: number
) => {
  try {
    const jobPayload = {
      userId,
      type: 'general' as const,
      title,
      body,
      data
    };

    const job = await notificationQueue.add('send-push-notification', jobPayload, {
      // use nullish coalescing so that an explicit 0 delay is preserved
      delay: delay ?? 0,
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 1000
      },
      // automatically prune completed and failed jobs
      removeOnComplete: 20,
      removeOnFail: 50
    });

    // mask userId to avoid logging full PII
    const maskedUserId = `${userId.slice(0, 4)}…${userId.slice(-4)}`;
    console.log(`Queued push notification job ${job.id} for user ${maskedUserId}`);

    return {
      success: true,
      jobId: job.id
    };

  } catch (error: any) {
    console.error('Failed to queue push notification:', error.message);
    throw error;
  }
};

/**
 * Get estimated processing time for recipe generation
 */
async function getEstimatedProcessingTime(): Promise<number> {
  try {
    const queueStats = await recipeGenerationQueue.getJobCounts();
    const waitingJobs = queueStats.waiting;
    const activeJobs = queueStats.active;

    // Estimate based on queue length (assuming 30 seconds per job)
    const estimatedSeconds = (waitingJobs + activeJobs) * 30;

    return Math.max(estimatedSeconds, 10); // Minimum 10 seconds

  } catch (error) {
    return 60; // Default to 1 minute if we can't get stats
  }
}

/**
 * Get job status by ID
 */
export const getJobStatus = async (jobId: string, queueType: 'recipe' | 'notification' | 'cleanup') => {
  try {
    let queue;
    switch (queueType) {
      case 'recipe':
        queue = recipeGenerationQueue;
        break;
      case 'notification':
        queue = notificationQueue;
        break;
      case 'cleanup':
        queue = cleanupQueue;
        break;
      default:
        throw new Error('Invalid queue type');
    }

    const job = await queue.getJob(jobId);

    if (!job) {
      return { success: false, error: 'Job not found' };
    }

    const state = await job.getState();
    const progress = job.progress();

    return {
      success: true,
      jobId: job.id,
      state,
      progress,
      data: job.data,
      createdAt: new Date(job.timestamp),
      processedOn: job.processedOn ? new Date(job.processedOn) : null,
      finishedOn: job.finishedOn ? new Date(job.finishedOn) : null,
      failedReason: job.failedReason
    };

  } catch (error: any) {
    return { success: false, error: error.message };
  }
};

/**
 * Get queue statistics
 */
export const getQueueStats = async () => {
  try {
    const [recipeStats, notificationStats, cleanupStats] = await Promise.all([
      recipeGenerationQueue.getJobCounts(),
      notificationQueue.getJobCounts(),
      cleanupQueue.getJobCounts()
    ]);

    return {
      success: true,
      stats: {
        recipeGeneration: recipeStats,
        notifications: notificationStats,
        cleanup: cleanupStats
      },
      timestamp: new Date().toISOString()
    };

  } catch (error: any) {
    return { success: false, error: error.message };
  }
};

/**
 * Queue a shelf life lookup job
 */
export const queueShelfLifeLookup = async (
  foodName: string,
  fridgeItemId?: string,
  userId?: string,
  category?: string,
  priority: 'high' | 'medium' | 'low' = 'medium'
) => {
  try {
    const jobPayload = {
      foodName,
      fridgeItemId,
      userId,
      category,
      priority
    };

    const job = await shelfLifeQueue.add('shelf-life-lookup', jobPayload, {
      priority: priority === 'high' ? 10 : priority === 'medium' ? 5 : 1,
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000
      },
      removeOnComplete: 10,
      removeOnFail: 20
    });

    console.log(`Queued shelf life lookup job ${job.id} for: ${foodName}`);

    return {
      success: true,
      jobId: job.id
    };

  } catch (error: any) {
    console.error('Failed to queue shelf life lookup:', error.message);
    throw error;
  }
};

export {
  processRecipeGeneration,
  queueDailyRecipeGeneration,
  processPushNotification,
  processShelfLifeLookup,
  processBatchShelfLifeLookup,
  queueUnknownFoodsProcessing
};
