import React, { useState } from 'react'
import { Image, StyleSheet, Text, View } from 'react-native'

import Page from '@/components/templates/Page'
import ScreenLoader from '@/components/atoms/loaders/Screen'
import Navbar from '@/components/templates/Navbar'
import BottomSheet from '@/components/templates/BottomSheet'
import Header from '@/components/templates/Header'

const Index = () => {
  const [bottomSheet, setBottomSheet] = useState(false)


  return (
    <Page noPaddingTop noBottomBar alignItems='center' justifyContent='space-between' page='fridge'>
            
      <Header text=' ' burgerMenu/>

      <View>
        
      </View>
    </Page>
  )
}

export default Index

const styles = StyleSheet.create({})