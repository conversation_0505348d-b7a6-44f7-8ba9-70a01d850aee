"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDailyLimit = exports.likeRecipe = exports.generateRecipe = exports.getRecipes = void 0;
const tslib_1 = require("tslib");
const services_1 = require("../services");
const recipeService = new services_1.RecipeService();
/**
 * Get user's recipes with filtering and pagination
 * GET /recipes
 */
const getRecipes = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const authReq = req;
        if (!authReq.user) {
            res.status(401).json({ success: false, message: 'Unauthorized', timestamp: new Date().toISOString() });
            return;
        }
        const userId = authReq.user._id;
        const queryParams = {
            page: parseInt(req.query.page) || 1,
            limit: parseInt(req.query.limit) || 20,
            sortBy: req.query.sortBy || 'createdAt',
            sortOrder: req.query.sortOrder || 'desc',
            source: req.query.source,
            isLiked: req.query.isLiked ? req.query.isLiked === 'true' : undefined,
            search: req.query.search,
            dateFrom: req.query.dateFrom,
            dateTo: req.query.dateTo
        };
        const result = yield recipeService.getUserRecipes(userId, queryParams);
        if (!result.success) {
            res.status(result.statusCode || 500).json({
                success: false,
                message: result.error,
                timestamp: new Date().toISOString()
            });
            return;
        }
        if (!result.data || !result.data.recipes) {
            res.status(500).json({
                success: false,
                message: 'Missing data',
                timestamp: new Date().toISOString()
            });
            return;
        }
        res.status(200).json({
            success: true,
            data: result.data.recipes,
            pagination: result.data.pagination,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            timestamp: new Date().toISOString()
        });
    }
});
exports.getRecipes = getRecipes;
/**
 * Generate a new recipe from selected fridge items
 * POST /recipes/generate
 */
const generateRecipe = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const authReq = req;
        if (!authReq.user) {
            res.status(401).json({ success: false, message: 'Unauthorized', timestamp: new Date().toISOString() });
            return;
        }
        const userId = authReq.user._id;
        const { fridgeItemIds } = req.body;
        // Basic validation
        if (!fridgeItemIds || !Array.isArray(fridgeItemIds) || fridgeItemIds.length === 0) {
            res.status(400).json({
                success: false,
                message: 'fridgeItemIds array is required and must not be empty',
                timestamp: new Date().toISOString()
            });
            return;
        }
        // Determine user tier from subscription plan
        const userTier = ((_a = authReq.user.finances) === null || _a === void 0 ? void 0 : _a.subscription_plan) === 'free' ? 'FREE' : 'PRO';
        const result = yield recipeService.generateRecipe(userId, fridgeItemIds, userTier);
        if (!result.success) {
            if (result.statusCode === 429) {
                // Rate limit error
                res.status(429).json({
                    success: false,
                    message: result.error,
                    details: result.data,
                    timestamp: new Date().toISOString()
                });
                return;
            }
            res.status(result.statusCode || 500).json({
                success: false,
                message: result.error,
                timestamp: new Date().toISOString()
            });
            return;
        }
        res.status(201).json({
            success: true,
            data: result.data.recipe,
            usageInfo: result.data.usageInfo,
            message: result.data.isFromCache
                ? 'Recipe retrieved from existing recipes'
                : 'New recipe generated successfully',
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            timestamp: new Date().toISOString()
        });
    }
});
exports.generateRecipe = generateRecipe;
/**
 * Toggle like status of a recipe
 * POST /recipes/:id/like
 */
const likeRecipe = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const authReq = req;
        if (!authReq.user) {
            res.status(401).json({ success: false, message: 'Unauthorized', timestamp: new Date().toISOString() });
            return;
        }
        const userId = authReq.user._id;
        const recipeId = req.params.id;
        if (!recipeId) {
            res.status(400).json({
                success: false,
                message: 'Recipe ID is required',
                timestamp: new Date().toISOString()
            });
            return;
        }
        const result = yield recipeService.likeRecipe(userId, recipeId);
        if (!result.success) {
            res.status(result.statusCode || 500).json({
                success: false,
                message: result.error,
                timestamp: new Date().toISOString()
            });
            return;
        }
        res.status(200).json({
            success: true,
            data: result.data,
            message: 'Recipe like status updated successfully',
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            timestamp: new Date().toISOString()
        });
    }
});
exports.likeRecipe = likeRecipe;
/**
 * Get user's daily recipe generation limit information
 * GET /recipes/daily-limit
 */
const getDailyLimit = (req, res) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const authReq = req;
        if (!authReq.user) {
            res.status(401).json({ success: false, message: 'Unauthorized', timestamp: new Date().toISOString() });
            return;
        }
        const userId = authReq.user._id;
        // Determine user tier from subscription plan
        const userTier = ((_a = authReq.user.finances) === null || _a === void 0 ? void 0 : _a.subscription_plan) === 'free' ? 'FREE' : 'PRO';
        const result = yield recipeService.getDailyLimitInfo(userId, userTier);
        if (!result.success) {
            res.status(result.statusCode || 500).json({
                success: false,
                message: result.error,
                timestamp: new Date().toISOString()
            });
            return;
        }
        res.status(200).json({
            success: true,
            data: result.data,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            timestamp: new Date().toISOString()
        });
    }
});
exports.getDailyLimit = getDailyLimit;
