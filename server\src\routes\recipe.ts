import { Router } from 'express';
import {
  AuthenticateTokenOAuth,
  generalRateLimit,
  recipeGenerationRateLimit,
  validateGenerateRecipe,
  validateRecipesQuery,
  validateMongoId,
  validatePagination
} from '../middleware';
import {
  getRecipes,
  generateRecipe,
  likeR<PERSON>ipe,
  getDailyLimit
} from '../controllers/recipe';

const router = Router();

// Apply authentication middleware to all routes
router.use(AuthenticateTokenOAuth);

// GET /recipes - Get user's recipes with filtering
router.get('/', generalRateLimit, validateRecipesQuery, validatePagination, getRecipes);

// POST /recipes/generate - Generate recipe from selected fridge items
router.post('/generate', recipeGenerationRateLimit, validateGenerateRecipe, generateRecipe);

// POST /recipes/:id/like - Toggle like status
router.post('/:id/like', generalRateLimit, validateMongoId, likeRecipe);

// GET /recipes/daily-limit - Get current usage vs limit
router.get('/daily-limit', generalRateLimit, getDailyLimit);

export default router;
