import React from 'react'
import { StyleSheet } from 'react-native'
import Svg, { <PERSON>, <PERSON> } from 'react-native-svg'

const BurgerIcon = () => {
    return (

        <Svg id="Capa_1" enable-background="new 0 0 510.901 510.901" height="50" viewBox="0 0 510.901 510.901" width="50">
            <G id="XMLID_356_">
            <Path id="XMLID_1140_" d="m488.138 277.51c9.449-9.369 15.312-22.349 15.312-36.675 0-21.53-13.239-40.019-32-47.774v-60.942-.834c.001-72.391-58.893-131.285-131.283-131.285h-169.432c-72.391 0-131.284 58.894-131.284 131.284v.834 60.942c-18.761 7.755-32 26.244-32 47.774 0 14.326 5.863 27.306 15.312 36.675-9.449 9.369-15.312 22.349-15.312 36.675 0 21.53 13.239 40.018 32 47.773v103.942c0 24.813 20.187 45 45 45h342c24.813 0 45-20.187 45-45v-103.94c18.761-7.755 32-26.244 32-47.773 0-14.327-5.864-27.306-15.313-36.676zm-36.362-15h-113.319l115.679-43.22c10.845 1.18 19.314 10.391 19.314 21.544.001 11.953-9.723 21.676-21.674 21.676zm-196.325-1.012-113.319-42.338h226.637zm-186-130.214c0-55.848 45.435-101.284 101.284-101.284h169.432c55.849 0 101.284 45.436 101.284 101.284v.834h-372zm372 30.835v27.041h-372v-27.041zm-404 78.716c0-11.154 8.47-20.364 19.314-21.544l115.679 43.22h-113.319c-11.951-.001-21.674-9.724-21.674-21.676zm404 225.066c0 8.271-6.729 15-15 15h-342c-8.271 0-15-6.729-15-15v-43h372zm-372-73v-27.041h372v27.041zm382.325-57.041h-392.651c-11.951 0-21.675-9.723-21.675-21.675s9.724-21.675 21.675-21.675h392.65c11.951 0 21.675 9.724 21.675 21.675s-9.723 21.675-21.674 21.675z"/>
            <Path id="XMLID_1157_" d="m333.451 58.06h30v28h-30z"/>
            <Path id="XMLID_1158_" d="m240.451 75.06h30v28h-30z"/>
            <Path id="XMLID_1159_" d="m147.451 58.06h30v28h-30z"/>
            </G>
        </Svg>
    )
}

export default BurgerIcon

const styles = StyleSheet.create({})
