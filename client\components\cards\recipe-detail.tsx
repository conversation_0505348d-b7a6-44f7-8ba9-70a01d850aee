import { Clock, RefreshCcw, Refresh<PERSON>cwDot, Refrigerator } from "lucide-react";
import AlertCard from "../global/alert-card";
import Button from "../global/button";
import MobileModal from "./mobile-modal";
import { useState } from "react";
import Image from "next/image";

export interface Recipe {
    id: string
    name: string
    description: string
    ingredients: string[]
    steps: string[]
    prepTime: number
    calories: number
    createdAt: number
}

export function RecipeDetail({ recipe }: { recipe: Recipe }) {
    const [removalIngredientsModalOpen, setRemovalIngredientsModalOpen] = useState(false)


    return (
        <div className="w-full">
            <div className="p-6 border border-gray-200 rounded-lg shadow-md bg-white">

                {/* Title */}
                <div className="w-full">
                    <h1 className="text-2xl font-bold mb-2">{recipe.name}</h1>
                    <div className="flex items-center text-muted-foreground mb-4">
                        <Clock className="h-4 w-4 mr-1" />
                        <span>{recipe.prepTime} mins</span>
                        <span className="mx-2">•</span>
                        <span>{recipe.calories} calories</span>
                    </div>
                </div>

                {/* Form Recipe Preparation */}
                <div className="w-full">
                    <AlertCard
                        variant="success"
                        title="Are you preparing this recipe?"
                        description="Let us know if you tried this recipe! By learning your preferences and refining our recipes, we can improve your personalized meal planner and help keep your fridge organized"
                        className="mb-2"
                    />
                    <div className="w-full flex-col flex gap-1 mb-4">
                        <Button
                            variant="primary"
                            text={{ color: "white" }}

                            onClick={() => {
                                console.log("Ingredients removed from fridge");
                            }}
                        >
                            Yes
                        </Button>
                        <Button
                            variant="primary"
                            text={{ color: "white" }}
                            button={{ color: '#00000040' }}
                            onClick={() => {
                                console.log("Ingredients removed from fridge");
                            }}
                        >
                            Generate New
                            <RefreshCcwDot size={17} className="mx-2" />
                        </Button>
                    </div>
                </div>

                {/* Description */}
                <p className="mb-3 text-muted-foreground">
                    {recipe.description}
                </p>

                {/* Ingredients */}
                <div className="mb-6">
                    <h2 className="text-lg font-semibold mb-2">Ingredients</h2>
                    <ul className="list-disc pl-5 space-y-1">
                        {recipe.ingredients.map((ingredient: string, index: number) => (
                            <li key={index}>{ingredient}</li>
                        ))}
                    </ul>
                </div>

                {/* Instructions */}
                <div>
                    <h2 className="text-lg font-semibold mb-2">Instructions</h2>
                    <ol className="list-decimal pl-5 space-y-3">
                        {recipe.steps.map((step, index) => (
                            <li key={index}>{step}</li>
                        ))}
                    </ol>
                </div>

                {/* Remove ingredients form */}
                <div className="w-full">
                    <AlertCard
                        title="Remove used ingredients?"
                        description="We can remove the ingredients used in the recipe from the items in your fridge"
                        className="mt-6 mb-2"
                    />
                    <Button
                        variant="primary"
                        text={{
                            color: "white",
                        }}
                        button={{
                            color: 'tomato'
                        }}
                        onClick={() => {
                            // Handle the action here
                            setRemovalIngredientsModalOpen(true)
                        }}
                    >
                        Remove Ingredients from Fridge
                        <Refrigerator size={16} className="mx-2" />
                    </Button>
                </div>

                {/* Modal selection removal items used */}
                <MobileModal isOpen={removalIngredientsModalOpen} onClose={() => setRemovalIngredientsModalOpen(false)}>
                    <AlertCard
                        title="Attention"
                        description="We will list the ingredients used from your fridge, select if you finished the item, or some is left and will be stored back in the fridge"
                        className="mb-2"
                    />
                    <h2 className="text-lg font-semibold mb-2">Items used</h2>


                    <ItemFormRemove itemName="Banana" itemId="1" expiryDate={'01/20/2025'} />

                    <Button onClick={() => { }} variant="primary" text={{ color: 'white' }}>
                        Continue
                    </Button>
                </MobileModal>

            </div>
        </div>
    )
}


interface itemFormRemoveProps {
    itemName: string,
    itemId: string,
    expiryDate?: string
}

const ItemFormRemove = ({ itemName, itemId, expiryDate }: itemFormRemoveProps) => {
    const [isDelete, setIsDelete] = useState(false)

    return (
        <div className="text-black p-4 bg-gray-100 rounded-lg mb-2">
            <div className="flex flex-row justify-between items-center mb-3">
                <div className="flex flex-row gap-1">
                    <Image
                        src='/icons/assets_hazel_1.svg'
                        height={20}
                        width={20}
                        alt=""
                    />
                    <p className="">{itemName}</p>
                </div>
                {
                    expiryDate &&
                    <p className="text-xs">
                        Expiry: 
                        &nbsp;
                        {
        
                            new Date(expiryDate).toLocaleString('en-US',{
                                day: '2-digit',
                                month: '2-digit',
                                year: '2-digit'
                            })
                        }
                    </p>
                }

            </div>

            <div className="flex flex-row items-center gap-2" onClick={() => setIsDelete(true)}>
                <div className={`w-4 h-4 border rounded-full flex items-center justify-center`}>
                    {
                        isDelete &&
                        <div className="bg-black w-2 h-2 rounded-full" />
                    }
                </div>
                <p> Yes, i finished this item </p>
            </div>

            <div className="flex flex-row items-center gap-2" onClick={() => setIsDelete(false)}>
                <div className={`w-4 h-4 border rounded-full flex items-center justify-center`}>
                    {
                        !isDelete &&
                        <div className="bg-black w-2 h-2 rounded-full" />
                    }
                </div>
                <p> No, there is still some left </p>
            </div>
        </div>
    )
}