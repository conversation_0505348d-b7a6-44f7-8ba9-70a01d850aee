import dotenv from 'dotenv';
dotenv.config();

import cors from 'cors';
import chalk from 'chalk';
import express from 'express';
import { errorHandler} from './middleware';

import { notifications, oauth, fridge, recipe } from './routes';
import connectDatabase from './database/connection';
import { initializeJobSystem } from './jobs';



// Create express app
const app = express();

// Middlewares
app.use(cors());
app.use(express.json());
app.use(errorHandler);

// Routes
app.use('/notifications', notifications);
app.use('/oauth', oauth);
app.use('/fridge', fridge);
app.use('/recipes', recipe);






const startServer = async () => {
  console.log(chalk.green(`[SERVER] Starting`));
  const PORT = process.env.PORT || 4000;
  try {
    // Await database connection
    await connectDatabase();
    console.log(chalk.green(`[SERVER] Database Connected`));

    // Initialize job system (CRON jobs and background processing)
    await initializeJobSystem();
    console.log(chalk.green(`[SERVER] Job System Initialized`));

    // Start the server after successful database connection
    const server = app.listen(PORT, () => {
      console.log(chalk.green(`[SERVER] Running on`), chalk.yellow.underline(`http://localhost:${PORT}`));
    });

    /* Handle unhandled promise rejections */
    process.on('unhandledRejection', (err) => {
      if (err instanceof Error) {
        console.log(chalk.red(`[ERROR] Unhandled Rejection: ${err.message}`));
      }
      // Close server & exit process
      server.close(() => process.exit(1));
    });
  } catch (error: any) {
    console.log(chalk.red(`[DATABASE] Connection failed: ${error.message}`));
    process.exit(1);
  }
};


startServer();