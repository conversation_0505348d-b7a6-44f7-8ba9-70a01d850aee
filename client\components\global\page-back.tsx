'use client'

import Head from 'next/head'
import Appbar from './appbar'
import BottomN<PERSON> from './bottom-nav'
import Appbar<PERSON>ust<PERSON> from './appbar-custom'

interface Props {
	title?: string
	children: React.ReactNode

	noPadding?: boolean
	goBack?: () => void
}

const PageBack = ({ title, children, goBack, noPadding }: Props) => (
	<>
		{title ? (
			<Head>
				<title>Leftover Chef | {title}</title>
			</Head>
		) : null}


		<AppbarCustom goBack={goBack} title={title} />



		<main
			/**
			 * Padding top = `appbar` height
			 * Padding bottom = `bottom-nav` height
			*/
			className={`mx-auto max-w-screen-md pb-16 px-safe sm:pb-0 ${noPadding ? 'pt-6' : 'pt-20'}`}
		>
			<div className='p-6'>{children}</div>
		</main>
		<BottomNav />
	</>
)

export default PageBack
