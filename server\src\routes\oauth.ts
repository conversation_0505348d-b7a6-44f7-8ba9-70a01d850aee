import { Request, Response } from "express";
import { Account } from "../database/schemas";
import { AuthenticateTokenOAuth, signTokenOAuth, tryCatch } from "../middleware";
import { checkSubscriptionStatus } from "../controllers/subscriptions";
import express from "express";
import { verifyAppleIdentityToken } from "../controllers/oauth";
import { AuthenticatedRequest } from "../types";
import { generateRandomNumberString, hashPassword } from "../utils";

const router = express.Router();

router.post("/login/apple", async (req: Request, res: Response): Promise<any> => {
  try {
    const { identityToken } = req.body;

    if (!identityToken) {
      return res.status(400).json({ success: false, error: "Missing identityToken" });
    }

    const payload: any = await verifyAppleIdentityToken(identityToken);
    const appleId = payload.sub;
    const emailApple = payload.email;

    let account = await Account.findOne({ email: emailApple, password: hashPassword(appleId) });

    if (!account) {
      console.log("Creating new account for Apple user:", emailApple);
      account = new Account({
        email: emailApple,
        password: hashPassword(appleId), // no password required for Apple users or use appleId
        user: {
          name: "",
          surname: "",
          username: `apple_${appleId.slice(-6)}`,
          profile_picture: "",
          birthdate: "",
          type: "user",
        },
        booleans: {
          isVerified: true,
          isAdmin: false,
        },
        // Other fields will fall back to schema defaults
      });

      await account.save();
    }

    const token = signTokenOAuth(emailApple, hashPassword(appleId));

    res.status(200).json({ success: true, token, account });
  } catch (error) {
    console.error("Apple login error:", error);
    res.status(401).json({ success: false, error: "Invalid or expired identity token" });
  }
});

router.post("/login/google", async (req: Request, res: Response): Promise<any> => {
  try {
    const { id, email, name, surname, photo } = req.body;


    let account = await Account.findOne({ email: email, password: hashPassword(id) });

    if (!account) {
      console.log("Creating new account for Google user:", email);
      account = new Account({
        email: email,
        password: hashPassword(id), // no password required for Apple users or use appleId
        user: {
          name: name ?? "",
          surname: surname ?? "",
          username: `${name}-${generateRandomNumberString(4)}`,
          profile_picture: photo,
          birthdate: "",
          type: "user",
        },
        booleans: {
          isVerified: true,
          isAdmin: false,
        },
        // Other fields will fall back to schema defaults
      });

      await account.save();
    }

    const token = signTokenOAuth(email, hashPassword(id));

    res.status(200).json({ success: true, token, account });
  } catch (error) {
    console.error("Google login error:", error);
    res.status(401).json({ success: false, error: "Invalid or expired identity token" });
  }
});

router.route("/authenticate").get(AuthenticateTokenOAuth, tryCatch(async (req: Request, res: Response): Promise<any> => {
  const authReq = req as AuthenticatedRequest;

  if (!authReq.user) {
    return res
      .status(401)
      .json({ success: false, message: "Unauthorized: authenticated user not found" });
  }
  // Normalize the user ID to ensure it's a string
  const normalizedId = String(authReq.user._id);

  const subscription = await checkSubscriptionStatus(normalizedId);

  // Create sanitized user object with only safe fields
  const sanitizedUser = {
    id: authReq.user._id,
    email: authReq.user.email,
    name: authReq.user.user?.name,
    surname: authReq.user.user?.surname,
    username: authReq.user.user?.username,
    type: authReq.user.user?.type
  };

  res
    .status(200)
    .json({ success: true, message: "Authorized Access", data: sanitizedUser, subscription });
}));

router.route("/dev")
.get(tryCatch(async (req: Request, res: Response): Promise<any> => {
  try {
    const firstAccount = await Account.findOne();
    
    if (!firstAccount) {
      return res.status(404).json({ success: false, error: "No users found in database" });
    }

    const token = signTokenOAuth(firstAccount.email, firstAccount.password);
    
    res.status(200).json({ success: true, token, account: firstAccount });
  } catch (error) {
    console.error("Dev login error:", error);
    res.status(500).json({ success: false, error: "Failed to generate dev token" });
  }
}));

export default router;
