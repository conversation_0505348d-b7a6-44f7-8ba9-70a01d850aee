import type { MetadataRoute } from 'next'
 
export default function manifest(): MetadataRoute.Manifest {
  return {
    name: 'LeftoverChef',
    short_name: 'LeftoverChef',
    description: 'AI Agent that tracks your fridge and pantry items, less waste, more savings.',
    start_url: '/auth',
    display: 'standalone',
    background_color: '#ffffff',
    theme_color: '#ffffff',
    icons: [
      {
        src: '/logo/leftover-chef-leaflogo.png',
        sizes: '192x192',
        type: 'image/png',
      },
      {
        src: '/logo/leftover-chef-leaflogo.png',
        sizes: '512x512',
        type: 'image/png',
      },
    ],
  }
}