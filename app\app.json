{"expo": {"name": "<PERSON>", "slug": "Leftover<PERSON><PERSON><PERSON>", "version": "1.0.1", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "app", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"buildNumber": "25.06.01.1", "supportsTablet": true, "bundleIdentifier": "hazel.leftover-chef.xyz", "usesAppleSignIn": true, "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "UIBackgroundModes": ["remote-notification"]}, "appleTeamId": "8Z69NK39Q7"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["expo-apple-authentication"], "expo-secure-store", ["@react-native-google-signin/google-signin", {"iosUrlScheme": "com.googleusercontent.apps.684414254538-iea4399buqu70spud45kkq457i5mpckv"}], "expo-localization"], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "7293a60c-22ca-44af-87ed-31f83f1e59b2"}}}}