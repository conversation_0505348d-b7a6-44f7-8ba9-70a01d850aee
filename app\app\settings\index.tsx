import React from 'react'
import { signInWithApple } from '@/utils/AppleAuth'
import { Image, StyleSheet, Text, View } from 'react-native'

import Page from '@/components/templates/Page'
import ScreenLoader from '@/components/atoms/loaders/Screen'
import Navbar from '@/components/templates/Navbar'

const Index = () => {




  return (
    <Page alignItems='center' justifyContent='space-between' >
      
      <View>
        
      </View>
      <Navbar/>
    </Page>
  )
}

export default Index

const styles = StyleSheet.create({})