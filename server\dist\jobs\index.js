"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeJobSystem = exports.getCronJobStatus = exports.stopCronJobs = exports.initializeCronJobs = exports.processPushNotification = exports.queueDailyRecipeGeneration = exports.processRecipeGeneration = exports.getQueueStats = exports.getJobStatus = exports.queuePushNotification = exports.queueManualRecipeGeneration = exports.initializeJobProcessors = exports.redis = exports.cleanupQueue = exports.notificationQueue = exports.recipeGenerationQueue = void 0;
const tslib_1 = require("tslib");
// Export queue configurations
var queue_1 = require("./queue");
Object.defineProperty(exports, "recipeGenerationQueue", { enumerable: true, get: function () { return queue_1.recipeGenerationQueue; } });
Object.defineProperty(exports, "notificationQueue", { enumerable: true, get: function () { return queue_1.notificationQueue; } });
Object.defineProperty(exports, "cleanupQueue", { enumerable: true, get: function () { return queue_1.cleanupQueue; } });
Object.defineProperty(exports, "redis", { enumerable: true, get: function () { return queue_1.redis; } });
// Export job processors
var processors_1 = require("./processors");
Object.defineProperty(exports, "initializeJobProcessors", { enumerable: true, get: function () { return processors_1.initializeJobProcessors; } });
Object.defineProperty(exports, "queueManualRecipeGeneration", { enumerable: true, get: function () { return processors_1.queueManualRecipeGeneration; } });
Object.defineProperty(exports, "queuePushNotification", { enumerable: true, get: function () { return processors_1.queuePushNotification; } });
Object.defineProperty(exports, "getJobStatus", { enumerable: true, get: function () { return processors_1.getJobStatus; } });
Object.defineProperty(exports, "getQueueStats", { enumerable: true, get: function () { return processors_1.getQueueStats; } });
Object.defineProperty(exports, "processRecipeGeneration", { enumerable: true, get: function () { return processors_1.processRecipeGeneration; } });
Object.defineProperty(exports, "queueDailyRecipeGeneration", { enumerable: true, get: function () { return processors_1.queueDailyRecipeGeneration; } });
Object.defineProperty(exports, "processPushNotification", { enumerable: true, get: function () { return processors_1.processPushNotification; } });
// Export scheduler functions
var scheduler_1 = require("./scheduler");
Object.defineProperty(exports, "initializeCronJobs", { enumerable: true, get: function () { return scheduler_1.initializeCronJobs; } });
Object.defineProperty(exports, "stopCronJobs", { enumerable: true, get: function () { return scheduler_1.stopCronJobs; } });
Object.defineProperty(exports, "getCronJobStatus", { enumerable: true, get: function () { return scheduler_1.getCronJobStatus; } });
// Initialize everything
const initializeJobSystem = () => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        console.log('Initializing job system...');
        // Initialize job processors first
        const { initializeJobProcessors } = yield Promise.resolve().then(() => tslib_1.__importStar(require('./processors')));
        yield initializeJobProcessors();
        // Then initialize CRON jobs
        const { initializeCronJobs } = yield Promise.resolve().then(() => tslib_1.__importStar(require('./scheduler')));
        yield initializeCronJobs();
        console.log('Job system initialized successfully');
        return { success: true };
    }
    catch (error) {
        console.error('Failed to initialize job system:', error.message);
        throw error;
    }
});
exports.initializeJobSystem = initializeJobSystem;
