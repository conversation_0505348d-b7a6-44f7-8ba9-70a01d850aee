import mongoose, { Document, Model } from 'mongoose';

export interface IFoodShelfLife extends Document {
  foodName: string;
  normalizedFoodName: string; // For fuzzy matching
  averageShelfLifeDays: number;
  category?: string;
  source: 'DATABASE' | 'AI_GENERATED' | 'USER_PROVIDED';
  confidence: number; // 0-1 scale
  storageConditions?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IFoodShelfLifeModel extends Model<IFoodShelfLife> {
  findByFoodName(foodName: string): Promise<IFoodShelfLife | null>;
  findSimilarFoods(foodName: string, limit?: number): Promise<IFoodShelfLife[]>;
  createOrUpdate(data: Partial<IFoodShelfLife>): Promise<IFoodShelfLife>;
}

const foodShelfLifeSchema = new mongoose.Schema<IFoodShelfLife, IFoodShelfLifeModel>({
  foodName: {
    type: String,
    required: [true, 'Food name is required'],
    trim: true,
    maxlength: [100, 'Food name cannot exceed 100 characters'],
    minlength: [1, 'Food name cannot be empty']
  },
  normalizedFoodName: {
    type: String,
    required: true,
    lowercase: true,
    trim: true,
    index: true
  },
  averageShelfLifeDays: {
    type: Number,
    required: [true, 'Average shelf life is required'],
    min: [1, 'Shelf life must be at least 1 day'],
    max: [365, 'Shelf life cannot exceed 365 days'],
    validate: {
      validator: function(value: number) {
        return Number.isInteger(value);
      },
      message: 'Shelf life must be a whole number of days'
    }
  },
  category: {
    type: String,
    trim: true,
    maxlength: [50, 'Category cannot exceed 50 characters'],
    enum: {
      values: [
        'fruits', 'vegetables', 'dairy', 'meat', 'poultry', 'seafood', 
        'grains', 'legumes', 'herbs', 'spices', 'condiments', 'beverages',
        'baked_goods', 'frozen', 'canned', 'dried', 'other'
      ],
      message: 'Invalid food category'
    }
  },
  source: {
    type: String,
    required: [true, 'Source is required'],
    enum: {
      values: ['DATABASE', 'AI_GENERATED', 'USER_PROVIDED'],
      message: 'Source must be DATABASE, AI_GENERATED, or USER_PROVIDED'
    },
    default: 'DATABASE'
  },
  confidence: {
    type: Number,
    required: [true, 'Confidence score is required'],
    min: [0, 'Confidence must be between 0 and 1'],
    max: [1, 'Confidence must be between 0 and 1'],
    default: 0.8
  },
  storageConditions: {
    type: String,
    trim: true,
    maxlength: [200, 'Storage conditions cannot exceed 200 characters']
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for efficient querying
foodShelfLifeSchema.index({ normalizedFoodName: 1 });
foodShelfLifeSchema.index({ foodName: 'text' }); // Text index for fuzzy search
foodShelfLifeSchema.index({ category: 1, normalizedFoodName: 1 });
foodShelfLifeSchema.index({ source: 1, confidence: -1 });
foodShelfLifeSchema.index({ createdAt: -1 });

// Pre-save middleware to normalize food name
foodShelfLifeSchema.pre('save', function(next) {
  if (this.isModified('foodName')) {
    this.normalizedFoodName = this.foodName.toLowerCase().trim()
      .replace(/s$/, '') // Remove plural 's'
      .replace(/[^a-z0-9\s]/g, '') // Remove special characters
      .replace(/\s+/g, ' ') // Normalize spaces
      .trim();
  }
  next();
});

// Static method to find by food name with fuzzy matching
foodShelfLifeSchema.statics.findByFoodName = async function(foodName: string): Promise<IFoodShelfLife | null> {
  const normalizedName = foodName.toLowerCase().trim()
    .replace(/s$/, '')
    .replace(/[^a-z0-9\s]/g, '')
    .replace(/\s+/g, ' ')
    .trim();

  // First try exact match on normalized name
  let result = await this.findOne({ normalizedFoodName: normalizedName });
  
  if (result) {
    return result;
  }

  // Try partial match
  result = await this.findOne({
    normalizedFoodName: { $regex: normalizedName, $options: 'i' }
  });

  if (result) {
    return result;
  }

  // Try text search as fallback
  const textResults = await this.find(
    { $text: { $search: foodName } },
    { score: { $meta: 'textScore' } }
  ).sort({ score: { $meta: 'textScore' } }).limit(1);

  return textResults.length > 0 ? textResults[0] : null;
};

// Static method to find similar foods
foodShelfLifeSchema.statics.findSimilarFoods = async function(
  foodName: string, 
  limit: number = 5
): Promise<IFoodShelfLife[]> {
  const normalizedName = foodName.toLowerCase().trim()
    .replace(/s$/, '')
    .replace(/[^a-z0-9\s]/g, '')
    .replace(/\s+/g, ' ')
    .trim();

  // Use text search to find similar items
  const results = await this.find(
    { $text: { $search: foodName } },
    { score: { $meta: 'textScore' } }
  )
  .sort({ score: { $meta: 'textScore' }, confidence: -1 })
  .limit(limit);

  // If text search doesn't yield results, try regex
  if (results.length === 0) {
    return await this.find({
      normalizedFoodName: { $regex: normalizedName, $options: 'i' }
    })
    .sort({ confidence: -1 })
    .limit(limit);
  }

  return results;
};

// Static method to create or update shelf life data
foodShelfLifeSchema.statics.createOrUpdate = async function(
  data: Partial<IFoodShelfLife>
): Promise<IFoodShelfLife> {
  if (!data.foodName) {
    throw new Error('Food name is required');
  }

  const normalizedName = data.foodName.toLowerCase().trim()
    .replace(/s$/, '')
    .replace(/[^a-z0-9\s]/g, '')
    .replace(/\s+/g, ' ')
    .trim();

  // Try to find existing entry
  const existing = await this.findOne({ normalizedFoodName: normalizedName });

  if (existing) {
    // Update existing entry if new data has higher confidence or is from a better source
    const shouldUpdate = 
      !data.confidence || 
      data.confidence > existing.confidence ||
      (data.source === 'DATABASE' && existing.source !== 'DATABASE') ||
      (data.source === 'AI_GENERATED' && existing.source === 'USER_PROVIDED');

    if (shouldUpdate) {
      Object.assign(existing, data);
      return await existing.save();
    }
    return existing;
  }

  // Create new entry
  return await this.create({
    ...data,
    normalizedFoodName: normalizedName
  });
};

// Virtual for shelf life in weeks (for display purposes)
foodShelfLifeSchema.virtual('shelfLifeWeeks').get(function() {
  return Math.round((this.averageShelfLifeDays / 7) * 10) / 10; // Round to 1 decimal
});

// Virtual for confidence percentage
foodShelfLifeSchema.virtual('confidencePercentage').get(function() {
  return Math.round(this.confidence * 100);
});

export default mongoose.model<IFoodShelfLife, IFoodShelfLifeModel>('FoodShelfLife', foodShelfLifeSchema);
