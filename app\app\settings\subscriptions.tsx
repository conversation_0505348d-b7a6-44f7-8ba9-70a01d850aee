import React, { useContext, useState } from 'react'
import { Linking, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import { Ionicons } from "@expo/vector-icons"

import Page from '@/components/templates/Page'
import { ScrollView } from 'react-native-gesture-handler'
import Header from '@/components/templates/Header'
import { useSubscriptions } from '@/context/SubcriptionsContext'
import Toast from 'react-native-toast-message'
import ScreenSpinnerLoader from '@/components/atoms/loaders/ScreenSpinner'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { AuthContext } from '@/context/AuthContext'


const subscriptionPlans = [
  {
    product: {
      title: "free",
      identifier: "free",
      priceString: "$0.00",
    },
    packageType: "FOREVER",
    current: true,
    features: ["Intelligent Food Tracking", "Smart Expiry Notifications", "Store Up to 30 Items", "Delicious Daily Recipes"],
  },
  {
    product: {
      title: "free",
      identifier: "free",
      priceString: "$0.00",
    },
    packageType: "",
    features: [
      "Advanced AI Food Tracking",
      "Intelligent Expiry Alerts",
      "Unlimited Fridge Storage",
      "Unlimited Personalized Recipes",
      "Smart Grocery Management"
    ]
  },
]


const Index = () => {
  const insets = useSafeAreaInsets();
  const {sessionAuthentication} = useContext(AuthContext)
  const { packages, purchasePackage, currentSubscription } = useSubscriptions()
  const [selectedPlan, setSelectedPlan] = useState(currentSubscription)
  const [loading, setLoading] = useState(false)

  const handlePurchaseSubscription = async () => {
    if (selectedPlan == 'free') return
    //find the whole package
    const selectedPack = packages.find((p: any) => p.product.identifier == selectedPlan)
    try {
      setLoading(true)
      const call = await purchasePackage(selectedPack)
      if (!call) {
        Toast.show({
          type: 'error',
          text1: 'Error Purchasing Subscription',
        })

        setLoading(false)
        return
      }
      
      await sessionAuthentication()
      setLoading(false)

      Toast.show({
        type: 'success',
        text1: 'Subscription Activated',
      })

    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error Purchasing Subscription',
      })
    }
  }
  const handleDowngradeToFree = async () => {
    Linking.openURL('https://apps.apple.com/account/subscriptions')
  }
  const renderPlanCard = (plan: any) => (
    <TouchableOpacity
      key={plan.product.identifier}
      style={[
        styles.planCard,
        selectedPlan === plan.product.identifier && styles.selectedPlanCard,
        //plan.popular && styles.popularPlanCard,
      ]}
      onPress={() => setSelectedPlan(plan.product.identifier)}
    >
      {plan.product.identifier === 'hazel_c_01' && (
        <View style={styles.popularBadge}>
          <Text style={styles.popularBadgeText}>Most Popular</Text>
        </View>
      )}

      {plan.product.identifier === currentSubscription && (
        <View style={styles.currentBadge}>
          <Text style={styles.currentBadgeText}>Current Plan</Text>
        </View>
      )}

      <View style={styles.planHeader}>
        <Text style={styles.planName}>
          {
            plan.product.identifier == 'free' ? 'Free' :
            plan.product.identifier == 'hazel_c_01' ? 'Hazel Pro - Month' :
            plan.product.identifier == 'hazel_c_12' && 'Hazel Pro - Year'
          }
        </Text>
        <View style={styles.priceContainer}>
          <Text style={styles.planPrice}>{plan.product.priceString}</Text>
          <Text style={styles.planPeriod}>/ {plan.packageType}</Text>
        </View>
      </View>

      <View style={styles.featuresContainer}>
        {
          !plan.features ?
            subscriptionPlans[1].features.map((feature, index) => (
              <View key={index} style={styles.featureRow}>
                <Ionicons name="checkmark-circle" size={20} color="rgba(34,197,94,0.7)" />
                <Text style={styles.featureText}>{feature}</Text>
              </View>
            ))
            :
            plan.features.map((feature: any, index: any) => (
              <View key={index} style={styles.featureRow}>
                <Ionicons name="checkmark-circle" size={20} color="rgba(34,197,94,0.7)" />
                <Text style={styles.featureText}>{feature}</Text>
              </View>
            ))
        }
      </View>

      {selectedPlan === plan.product.identifier && (
        <View style={styles.selectedIndicator}>
          <Ionicons name="checkmark-circle" size={24} color="rgba(34,197,94,0.7)" />
        </View>
      )}
    </TouchableOpacity>
  )




  return (
    <Page noPaddingTop alignItems='center' justifyContent='space-between' >
      
      <Header buttonBack text=' ' />
      {
        loading &&
        <ScreenSpinnerLoader />
      }
      <ScrollView style={[styles.scrollView]} showsVerticalScrollIndicator={false}>
        {/* Title Section */}
        <View style={styles.titleSection}>
          <Text style={styles.mainTitle}>Choose Your Plan</Text>
          <Text style={styles.subtitle}>Upgrade to unlock premium features and get the most out of Hazel</Text>
        </View>

        {/* Subscription Plans */}
        <View style={styles.plansContainer}>
          {subscriptionPlans.slice(0, 1).map(renderPlanCard)}
          {packages.map(renderPlanCard)}
        </View>

        {/* Benefits Section */}
        <View style={styles.benefitsSection}>
          <Text style={styles.benefitsTitle}>Why upgrade?</Text>
          <View style={styles.benefitRow}>
            <Ionicons name="cart" size={24} color="rgba(34,197,94,0.7)" />
            <Text style={styles.benefitText}>Smart groceries, tells you if it's worth buying</Text>
          </View>
          <View style={styles.benefitRow}>
            <Ionicons name="fast-food" size={24} color="rgba(34,197,94,0.7)" />
            <Text style={styles.benefitText}>Recipes tailormade to your taste</Text>
          </View>
          <View style={styles.benefitRow}>
            <Ionicons name="calendar" size={24} color="rgba(34,197,94,0.7)" />
            <Text style={styles.benefitText}>Expiry recipes suggestions</Text>
          </View>
          <View style={styles.benefitRow}>
            <Ionicons name="notifications" size={24} color="rgba(34,197,94,0.7)" />
            <Text style={styles.benefitText}>Smart notifications before food expires</Text>
          </View>
        </View>
      </ScrollView>

      {/* Bottom Action */}
      {
        selectedPlan !== "free" &&
        selectedPlan !== currentSubscription &&
        (
          <View style={styles.bottomAction}>
            <TouchableOpacity onPress={handlePurchaseSubscription} style={styles.upgradeButton}>
              <Text style={styles.upgradeButtonText}>
                {selectedPlan === "hazel_c_01" ? "Upgrade to PRO Monthly" : "Upgrade to PRO Yearly"}
              </Text>
            </TouchableOpacity>
            <Text style={styles.termsText}>Cancel anytime. Terms and conditions apply.</Text>
          </View>
        )}

      {selectedPlan === "free" &&
        currentSubscription !== 'free' &&
        (
          <View style={styles.bottomAction}>
            <TouchableOpacity onPress={handleDowngradeToFree} style={[styles.upgradeButton, { backgroundColor: 'tomato' }]}>
              <Text style={styles.upgradeButtonText}>
                Downgrade to FREE
              </Text>
            </TouchableOpacity>
            <Text style={styles.termsText}>You can reactivate any time. Terms and conditions apply.</Text>
          </View>
        )}
    </Page>
  )
}

export default Index

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e9ecef",
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: "600",
    textAlign: "center",
    color: "#333",
  },
  headerSpacer: {
    width: 32,
  },
  scrollView: {
    flex: 1,
  },
  titleSection: {
    padding: 24,
    alignItems: "center",
    marginTop: 100,
    marginBottom: 30,
  },
  mainTitle: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
    lineHeight: 22,
  },
  plansContainer: {
    paddingHorizontal: 20,
    gap: 16,
  },
  planCard: {
    backgroundColor: "#fff",
    borderRadius: 16,
    padding: 20,
    borderWidth: 2,
    borderColor: "#e9ecef",
    position: "relative",
  },
  selectedPlanCard: {
    borderColor: "rgba(34,197,94,0.7)",
  },
  popularPlanCard: {
    borderColor: "rgba(34,197,94,0.7)",
  },
  popularBadge: {
    position: "absolute",
    top: -8,
    left: 20,
    backgroundColor: "rgba(34,197,94,0.7)",
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  popularBadgeText: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "600",
  },
  currentBadge: {
    position: "absolute",
    top: -8,
    right: 20,
    backgroundColor: "#6c757d",
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  currentBadgeText: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "600",
  },
  planHeader: {
    marginBottom: 20,
  },
  planName: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 8,
  },
  priceContainer: {
    flexDirection: "row",
    alignItems: "baseline",
  },
  planPrice: {
    fontSize: 32,
    fontWeight: "bold",
    color: "rgba(34,197,94,0.7)",
  },
  planPeriod: {
    fontSize: 16,
    color: "#666",
    marginLeft: 4,
  },
  featuresContainer: {
    gap: 7,
  },
  featureRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  featureText: {
    fontSize: 16,
    color: "#333",
    flex: 1,
  },
  selectedIndicator: {
    position: "absolute",
    top: 20,
    right: 20,
  },
  benefitsSection: {
    padding: 24,
    marginTop: 20,
  },
  benefitsTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 20,
  },
  benefitRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 16,
    marginBottom: 16,
  },
  benefitText: {
    fontSize: 16,
    color: "#333",
    flex: 1,
  },
  bottomAction: {
    padding: 20,
    backgroundColor: "#fff",
    borderTopWidth: 1,
    borderTopColor: "#e9ecef",
  },
  upgradeButton: {
    backgroundColor: "rgba(34,197,94,0.7)",
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: "center",
    marginBottom: 12,
  },
  upgradeButtonText: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "600",
  },
  termsText: {
    fontSize: 12,
    color: "#666",
    textAlign: "center",
  },
})