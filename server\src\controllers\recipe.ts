import { Request, Response } from 'express';
import { RecipeService } from '../services';
import { 
  GenerateRecipeDto,
  CreateRecipeDto,
  UpdateRecipeDto,
  RecipesQueryParams,
  AuthenticatedRequest,
  UserTier
} from '../types';

const recipeService = new RecipeService();

/**
 * Get user's recipes with filtering and pagination
 * GET /recipes
 */
export const getRecipes = async (req: Request, res: Response): Promise<void> => {
  try {
  const authReq = req as AuthenticatedRequest;
  if (!authReq.user) { res.status(401).json({ success:false, message:'Unauthorized', timestamp:new Date().toISOString()}); return; }
  const userId = authReq.user._id;
    const queryParams: RecipesQueryParams = {
      page: parseInt(req.query.page as string) || 1,
      limit: parseInt(req.query.limit as string) || 20,
      sortBy: req.query.sortBy as any || 'createdAt',
      sortOrder: req.query.sortOrder as any || 'desc',
      source: req.query.source as any,
      isLiked: req.query.isLiked ? req.query.isLiked === 'true' : undefined,
      search: req.query.search as string,
      dateFrom: req.query.dateFrom as string,
      dateTo: req.query.dateTo as string
    };

    const result = await recipeService.getUserRecipes(userId, queryParams);

    if (!result.success) {
      res.status(result.statusCode || 500).json({
        success: false,
        message: result.error,
        timestamp: new Date().toISOString()
      });
      return;
    }

    if (!result.data || !result.data.recipes) { 
      res.status(500).json({ 
        success: false, 
        message: 'Missing data', 
        timestamp: new Date().toISOString() 
      }); 
      return; 
    }
    res.status(200).json({
      success: true,
      data: result.data.recipes,
      pagination: result.data.pagination,
      timestamp: new Date().toISOString()
    });  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Generate a new recipe from selected fridge items
 * POST /recipes/generate
 */
export const generateRecipe = async (req: Request, res: Response): Promise<void> => {
  try {
  const authReq = req as AuthenticatedRequest;
  if (!authReq.user) { res.status(401).json({ success:false, message:'Unauthorized', timestamp:new Date().toISOString()}); return; }
  const userId = authReq.user._id;
    const { fridgeItemIds }: GenerateRecipeDto = req.body;

    // Basic validation
    if (!fridgeItemIds || !Array.isArray(fridgeItemIds) || fridgeItemIds.length === 0) {
      res.status(400).json({
        success: false,
        message: 'fridgeItemIds array is required and must not be empty',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // Determine user tier from subscription plan
  const userTier: UserTier = authReq.user.finances?.subscription_plan === 'free' ? 'FREE' : 'PRO';

    const result = await recipeService.generateRecipe(userId, fridgeItemIds, userTier);

    if (!result.success) {
      if (result.statusCode === 429) {
        // Rate limit error
        res.status(429).json({
          success: false,
          message: result.error,
          details: result.data,
          timestamp: new Date().toISOString()
        });
        return;
      }

      res.status(result.statusCode || 500).json({
        success: false,
        message: result.error,
        timestamp: new Date().toISOString()
      });
      return;
    }

    res.status(201).json({
      success: true,
      data: result.data.recipe,
      usageInfo: result.data.usageInfo,
      message: result.data.isFromCache 
        ? 'Recipe retrieved from existing recipes' 
        : 'New recipe generated successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Toggle like status of a recipe
 * POST /recipes/:id/like
 */
export const likeRecipe = async (req: Request, res: Response): Promise<void> => {
  try {
  const authReq = req as AuthenticatedRequest;
  if (!authReq.user) { res.status(401).json({ success:false, message:'Unauthorized', timestamp:new Date().toISOString()}); return; }
  const userId = authReq.user._id;
    const recipeId = req.params.id;

    if (!recipeId) {
      res.status(400).json({
        success: false,
        message: 'Recipe ID is required',
        timestamp: new Date().toISOString()
      });
      return;
    }

    const result = await recipeService.likeRecipe(userId, recipeId);

    if (!result.success) {
      res.status(result.statusCode || 500).json({
        success: false,
        message: result.error,
        timestamp: new Date().toISOString()
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: result.data,
      message: 'Recipe like status updated successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get user's daily recipe generation limit information
 * GET /recipes/daily-limit
 */
export const getDailyLimit = async (req: Request, res: Response): Promise<void> => {
  try {
  const authReq = req as AuthenticatedRequest;
  if (!authReq.user) { res.status(401).json({ success:false, message:'Unauthorized', timestamp:new Date().toISOString()}); return; }
  const userId = authReq.user._id;
    
    // Determine user tier from subscription plan
  const userTier: UserTier = authReq.user.finances?.subscription_plan === 'free' ? 'FREE' : 'PRO';

    const result = await recipeService.getDailyLimitInfo(userId, userTier);

    if (!result.success) {
      res.status(result.statusCode || 500).json({
        success: false,
        message: result.error,
        timestamp: new Date().toISOString()
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: result.data,
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  }
};
