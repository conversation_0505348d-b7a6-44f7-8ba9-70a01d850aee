import Image from 'next/image'
import React from 'react'

interface FridgeStatsPanelProps {
    clickRefill: () => void
}

function FridgeStatsPanel({ clickRefill }: FridgeStatsPanelProps) {
    return (

        <div
            style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                position: "relative",
                width: "100%",
                marginBottom: "20px",
            }}
        >
            <Image
                src="/logo/leftover-chef-fridge.png"
                alt="Leftover Chef Logo"
                width={400}
                height={400}
            />
            <div
                style={{
                    position: "absolute",
                    top: "75%",
                    left: "51%",
                    transform: "translate(-50%, -50%)",
                    backgroundColor: "white",
                    borderRadius: "20px",
                    padding: "10px 20px",
                    height: "40px",
                    border: "1px solid #00000050",
                    boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.1)",


                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                }}

                onClick={clickRefill}
            >
                Refill your fridge
            </div>
        </div>


    )
}

export default FridgeStatsPanel