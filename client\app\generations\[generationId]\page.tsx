'use client'


import { useEffect, useState, use } from "react";
import Page from "@/components/global/page";
import Section from "@/components/global/section";
import { RecipeSkeletonLoader } from "@/components/cards/skeleton-card";
import { RecipeDetail } from "@/components/cards/recipe-detail";



export default function Home({ params }: { params: Promise<{ generationId: string }> }) {
  const { generationId } = use(params);
 

  return (
    <Page noPadding>
      <Section>
        <div style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          height: "100%",
          width: "100%",
          color: 'black',
        }}>
          
          {/* <RecipeSkeletonLoader/> */}
          <RecipeDetail
            recipe={{
               id: generationId,
              name: "Recipe Name",
              description: "Recipe Description",
              ingredients: ["Ingredient 1", "Ingredient 2"],
              steps: ["Step 1", "Step 2"],
              prepTime: 30,
              calories: 200,
              createdAt: Date.now(),
            }}
          />
          {/* <p>{generationId}</p> */}
        </div>     
      </Section>
    </Page>
  );
}
