import { authenticateOAuth } from '@/services/api';
import { useRouter } from 'expo-router';
import React, { createContext, useState, useEffect } from 'react';
import { useSubscriptions } from './SubcriptionsContext';
import { useNotifications } from './NotificationsContext';

interface UserDataModel {
  email: string;
  password: string;
  user: {
    name: string;
    surname: string;
    username: string;
    profile_picture: string;
    birthdate: Date | string; // You can adjust the type here
    type: 'user' | 'admin' | 'operator';
  };
  contacts: {
    phone: string;
    address: string;
  };
  settings: {
    currency: string;
    preferred_language: string;
    timezone: string;
  };
  finances: {
    stripe_customer_id: string;
    stripe_payment_method: string;
    stripe_payment_methods: string[];
    billing_address: string;

    subscription_plan: string;
    subscription_expiry: Date
  };
  booleans: {
    isVerified: boolean;
    isAdmin: boolean;
  };
  tokens: {
    verificationToken?: string;
    passwordResetToken?: string;
  };
  notifications: {
    expo_push_token?: string;
  };
  createdAt?: Date
}


export const AuthContext = createContext({
  role: null as string | null,
  userData: null as UserDataModel | null,
  handleInitialAuthentication: async () => { },
  sessionAuthentication: async () => { }
});

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const router = useRouter()
  const { initializeRevenueCat, checkSubscriptionStatus } = useSubscriptions()
  const { initializeNotifications } = useNotifications()


  const [role, setRole] = useState(null)
  const [userData, setUserData] = useState(null)


  const handleInitialAuthentication = async () => {
    const call = await authenticateOAuth();

    if (!call.success) {
      router.replace('/auth/login')
    } else {

      //Save user data
      setUserData(call.data)
      setRole(call.data.user.type)
      const initRevenueCat = await initializeRevenueCat(call.data._id)
      const checkSubscription = await checkSubscriptionStatus(call.subscription.isPro, call.subscription.productIdentifier)
      const initNotifications = await initializeNotifications()
      
      
      //Go to dashboard
      router.replace('/(home)')
    }

  };
  const sessionAuthentication = async () => {
    const call = await authenticateOAuth();
    if (!call.success) {
      router.replace('/auth/login')
      return
    }

    setUserData(call.data)
    setRole(call.data.user.type)
    const initRevenueCat = await initializeRevenueCat(call.data._id)
    const checkSubscription= await checkSubscriptionStatus(call.subscription.isPro, call.subscription.productIdentifier)
    const initNotifications = await initializeNotifications()
   
  };



  return (
    <AuthContext.Provider value={{ handleInitialAuthentication, sessionAuthentication, role, userData }}>
      {children}
    </AuthContext.Provider>
  );
};