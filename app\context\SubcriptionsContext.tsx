import { useRouter } from 'expo-router';
import React, {
  createContext,
  useState,
  useCallback,
  useMemo,
  useContext,
} from 'react';
import Purchases from 'react-native-purchases';
import { Platform } from 'react-native';
import Toast from 'react-native-toast-message';

interface SubscriptionsContextProps {
  isReady: boolean;
  isPro: boolean;
  packages: any[];
  currentSubscription: string;
  checkSubscriptionStatus: (isPro: boolean, productIdentifier: string) => Promise<void>;
  purchasePackage: (pack: any) => Promise<boolean>;
  initializeRevenueCat: (userId: string) => Promise<void>;
}

const defaultContext: SubscriptionsContextProps = {
  isReady: false,
  isPro: false,
  packages: [],
  currentSubscription: 'free',
  checkSubscriptionStatus: async () => {},
  purchasePackage: async () => false,
  initializeRevenueCat: async () => {},
};

export const SubscriptionsContext = createContext<SubscriptionsContextProps>(defaultContext);

export const useSubscriptions = () => {
  return useContext(SubscriptionsContext);
};

const revenueCatAPIKeys = {
  apple: 'appl_DbhyhyZDTONIRGVAzOEfjqacwHx',
};

export const SubscriptionsProvider = ({ children }: { children: React.ReactNode }) => {
  const router = useRouter();

  const [isReady, setIsReady] = useState(false);
  const [packages, setPackages] = useState<any[]>([]);
  const [isPro, setIsPro] = useState(false);
  const [currentSubscription, setCurrentSubscription] = useState('free');

  const loadOfferings = useCallback(async () => {
    try {
      const offerings = await Purchases.getOfferings();
      if (offerings.current) {
        setPackages(offerings.current.availablePackages);
      }
    } catch (error) {
      console.error('Error loading offerings:', error);
    }
  }, []);

  const checkSubscriptionStatus = useCallback(
    async (isPro: boolean, productIdentifier: string) => {
      setIsPro(isPro);
      setCurrentSubscription(productIdentifier);
    },
    []
  );

  const purchasePackage = useCallback(async (pack: any) => {
    try {
      await Purchases.purchasePackage(pack);
      return true;
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'User cancelled subscription purchase',
      });
      return false;
    }
  }, []);

  const initializeRevenueCat = useCallback(
    async (userId: string) => {
      try {
        if (Platform.OS === 'ios') {
          await Purchases.configure({ apiKey: revenueCatAPIKeys.apple });
          await Purchases.logIn(userId);
        }
        setIsReady(true);
        await loadOfferings();
      } catch (error: any) {
        console.error('RevenueCat init error:', error.message);
      }
    },
    [loadOfferings]
  );

  const contextValue = useMemo(
    () => ({
      isReady,
      isPro,
      packages,
      currentSubscription,
      checkSubscriptionStatus,
      purchasePackage,
      initializeRevenueCat,
    }),
    [isReady, isPro, packages, currentSubscription, checkSubscriptionStatus, purchasePackage, initializeRevenueCat]
  );

  return (
    <SubscriptionsContext.Provider value={contextValue}>
      {children}
    </SubscriptionsContext.Provider>
  );
};
