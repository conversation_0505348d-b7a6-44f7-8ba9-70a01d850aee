import { NextFunction, Request, Response } from "express";
import { ZodError, ZodIssue } from 'zod';

class ErrorResponse extends Error {
  statusCode: number;
  details?: any;

  constructor(message: string, statusCode: number, details?: any) {
    // Ensure the message property is passed correctly to the Error class
    super(message);

    this.statusCode = statusCode;
    this.details = details;

    // This ensures the error's stack trace is correctly captured in the context of this class
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }

    // Ensure the name is set to the correct error class name
    this.name = this.constructor.name;
  }
}

// Custom error classes for specific scenarios
export class ValidationError extends ErrorResponse {
  constructor(message: string, details?: any) {
    super(message, 400, details);
    this.name = 'ValidationError';
  }
}

export class NotFoundError extends ErrorResponse {
  constructor(message: string = 'Resource not found') {
    super(message, 404);
    this.name = 'NotFoundError';
  }
}

export class UnauthorizedError extends ErrorResponse {
  constructor(message: string = 'Unauthorized access') {
    super(message, 401);
    this.name = 'UnauthorizedError';
  }
}

export class ForbiddenError extends ErrorResponse {
  constructor(message: string = 'Access forbidden') {
    super(message, 403);
    this.name = 'ForbiddenError';
  }
}

export class RateLimitError extends ErrorResponse {
  constructor(message: string, details?: any) {
    super(message, 429, details);
    this.name = 'RateLimitError';
  }
}

export class ServiceUnavailableError extends ErrorResponse {
  constructor(message: string = 'Service temporarily unavailable') {
    super(message, 503);
    this.name = 'ServiceUnavailableError';
  }
}


//Parse tricky errors
export const errorHandler = (err: any, req: Request, res: Response, next: NextFunction): void =>  {
  let error = { ...err };

  error.message = err.message;

  // Log error for development (you might want to use a proper logger in production)
  console.error("ERROR: ", {
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    timestamp: new Date().toISOString()
  });

  // Handle Zod validation errors
  if (err instanceof ZodError) {
    const validationErrors = err.issues.map((zodErr: ZodIssue) => ({
      field: zodErr.path.join('.'),
      message: zodErr.message,
      code: zodErr.code
    }));

    res.status(400).json({
      success: false,
      statusCode: 400,
      message: 'Validation failed',
      errors: validationErrors,
      timestamp: new Date().toISOString()
    });
    return;
  }

  // Handle custom error classes
  if (err instanceof ErrorResponse) {
    res.status(err.statusCode).json({
      success: false,
      statusCode: err.statusCode,
      message: err.message,
      details: err.details,
      timestamp: new Date().toISOString()
    });
    return;
  }

  //Mongoose bad ObjectId
  if (err.name === "CastError") {
    const message = `Resource not found with id of ${err.value}`;
    error = new ErrorResponse(message, 404);
  }

  //Mongoose validation error
  if (err.name === "ValidationError") {
    const validationErrors = Object.values(err.errors).map((value: any) => ({
      field: value.path,
      message: value.message,
      code: 'VALIDATION_ERROR',
      value: value.value
    }));

    res.status(400).json({
      success: false,
      statusCode: 400,
      message: 'Database validation failed',
      errors: validationErrors,
      timestamp: new Date().toISOString()
    });
    return;
  }

  //Mongoose duplicate key
  if (err.code === 11000 || err.code === 11001) {
    let message = "";
    const match = error.message.match(
      /index: (\w+)_1 dup key: { (\w+): "(.+)" }/
    );
    if (match) {
      const keyName = match[2];
      const duplicateValue = match[3];
      const formattedKey = keyName
        .split("_")
        .map((word: any) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ");
      message = `${formattedKey} ${duplicateValue} already exists`;
    } else {
      message = "Duplicate entry detected";
    }
    error = new ErrorResponse(message, 409);
  }

  // Handle JWT errors
  if (err.name === 'JsonWebTokenError') {
    error = new ErrorResponse('Invalid token', 401);
  }

  if (err.name === 'TokenExpiredError') {
    error = new ErrorResponse('Token expired', 401);
  }

  // Handle rate limiting errors
  if (err.status === 429) {
    res.status(429).json({
      success: false,
      statusCode: 429,
      message: 'Too many requests',
      details: {
        retryAfter: err.retryAfter || 60
      },
      timestamp: new Date().toISOString()
    });
    return;
  }

  // Default error response
  res.status(error.statusCode || 500).json({
    success: false,
    statusCode: error.statusCode || 500,
    message: error.message || "Internal Server Error",
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack }),
    timestamp: new Date().toISOString()
  });
};

// Middleware to wrap async route handlers with try/catch
export const tryCatch = (fn: (req: Request, res: Response, next: NextFunction) => Promise<any>) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch((error: ErrorResponse) => {
      console.error(`Unexpected Error - trycatch middlewrare - ${error.message}`)
      res.status(500).json({ success: false, error });
    });
  };
};


