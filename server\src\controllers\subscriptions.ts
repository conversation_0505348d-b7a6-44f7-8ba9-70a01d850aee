
import { Account } from "../database/schemas";
import { RevenueCatResponse } from "../types/revenue_cat";
import axios, { AxiosError, AxiosResponse } from "axios";

export const checkSubscriptionStatus = async (appUserId: string): Promise<any> => {
  try {
    const account = await Account.findById(appUserId).select('finances.subscription_plan finances.subscription_expiry')
    if (!account) {
      return {
        success: false,
        message: 'Account with that id does not exist'
      }
    }


    const response: AxiosResponse<RevenueCatResponse> = await axios.get(
      `https://api.revenuecat.com/v1/subscribers/${encodeURIComponent(appUserId)}`,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.REVENUECAT_API_KEY}`,
        },
      }
    );


    const customerInfo = response.data.subscriber;
    const hazelChefPro = customerInfo.entitlements['HazelChef PRO']



    if (hazelChefPro) {
      /* 
        User has entitlements, check for expiry
      */

      const expiresDate = hazelChefPro.expires_date && new Date(hazelChefPro.expires_date);

      //is currently (from API data - RevenueCat) active?
      const revenueCatStatus = isSubscriptionActive(expiresDate ? new Date(expiresDate) : new Date())
      const currentSubscriptionStatus = isSubscriptionActive(account.finances.subscription_expiry ? new Date(account.finances.subscription_expiry) : new Date())

      //console.log(revenueCatStatus)
      //console.log(currentSubscriptionStatus) 


      if (currentSubscriptionStatus.isPro != revenueCatStatus.isPro) {
        //If the current status coming live from revenue cat is different to whats saved on db, then update
        if (expiresDate instanceof Date && !isNaN(expiresDate.getTime())) {
          account.finances.subscription_expiry = expiresDate;
        }  
      } 

      const allowedPlans = ['free', 'hazel_c_01', 'hazel_c_12'] as const;
      if (allowedPlans.includes(hazelChefPro.product_identifier as any)) {
        account.finances.subscription_plan = hazelChefPro.product_identifier as typeof allowedPlans[number];
      }

      await account.save()

      return {
        isPro: revenueCatStatus.isPro,
        productIdentifier: revenueCatStatus.isPro ? hazelChefPro.product_identifier : 'free'
      }
    } else {
      //No entitlement User is on free plan


      account.finances.subscription_plan = 'free'
      await account.save()

      return {
        isPro: false,
        productIdentifier: 'free'
      }

    }

  } catch (error) {
    const axiosError = error as AxiosError;
    console.error('Error checking subscription status:', axiosError.message);
    /* if (axiosError.response) {
      console.error('Response data:', axiosError.response.data);
      console.error('Response status:', axiosError.response.status);
    } */
    return { isPro: false, productIdentifier: 'free' };
  }
};

//Check subscription status based on expiry date (compare with current date)
const isSubscriptionActive = ( subscription_expiry: Date ) => {

  // Get current UTC/Zulu date
  const currentDate = new Date();

  // Compare expiry date with current date
  if (subscription_expiry > currentDate) {
    return {
      success: true,
      isPro: true,
    };
  }

  return {
    success: false,
    message: 'Subscription has expired',
    isPro: false,
  };
};