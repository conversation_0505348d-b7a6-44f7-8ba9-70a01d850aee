{"name": "hazel-be", "version": "1.0.0", "description": "", "main": "index.ts", "scripts": {"buildstart": "node dist/index.js", "build": "tsc", "start": "npx ts-node ./dist/index.js", "dev": "nodemon", "nodemon": "nodemon --quiet --exec ts-node ./src/index.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.709.0", "@aws-sdk/lib-storage": "^3.810.0", "@aws-sdk/node-http-handler": "^3.370.0", "@polygon.io/client-js": "^7.3.2", "@types/aws-sdk": "^2.7.4", "@vercel/node": "^5.3.13", "api": "^6.1.1", "axios": "^1.9.0", "chalk": "^5.4.1", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.19.2", "express-session": "^1.18.1", "google-auth-library": "^9.15.1", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.2.0", "mongoose": "^8.3.2", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.16", "passport": "^0.7.0", "passport-apple": "^2.0.2", "passport-google-oauth20": "^2.0.0", "sharp": "^0.34.1", "stripe": "^17.4.0", "tslib": "^2.8.1", "uuid": "^11.1.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/mime-types": "^2.1.4", "@types/multer": "^1.4.12", "@types/node": "^22.13.5", "@types/nodemailer": "^6.4.17", "@types/passport-apple": "^2.0.3", "@types/passport-google-oauth20": "^2.0.16", "@types/stripe": "^8.0.417", "nodemon": "^3.1.9", "ts-node": "^10.9.2", "typescript": "^5.7.3"}}