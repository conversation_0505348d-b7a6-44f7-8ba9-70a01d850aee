"use client"

import type React from "react"

import { useEffect, useRef, useState } from "react"
import { useMediaQuery } from "@/hooks/use-mobile"

interface FloatingBackgroundProps {
  children?: React.ReactNode
}

export default function FloatingBackground({ children }: FloatingBackgroundProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 })
  const isMobile = useMediaQuery("(max-width: 768px)")

  // Circle positions
  const [positions, setPositions] = useState([
    { x: 120, y: 200, size: isMobile ? 10 : 200, speedX: 0.9, speedY: 0.9, color: "#b8e603a0" },
    { x: 180, y: 550, size: isMobile ? 90 : 200, speedX: 0.9, speedY: 0.9, color: "#ff680090" },
    { x: 120, y: 350, size: isMobile ? 90 : 200, speedX: 0.9, speedY: 0.9, color: "#FF6347" },
    { x: 180, y: 800, size: isMobile ? 90 : 200, speedX: 0.9, speedY: 0.9, color: "#FDFD96" },
    { x: 90, y: 300, size: isMobile ? 90 : 200, speedX: 0.9, speedY: 0.9, color: "#5A93DBa9" },
  ])

  // Update dimensions on resize
  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        setDimensions({
          width: window.innerWidth,
          height: window.innerHeight,
        })
      }
    }

    updateDimensions()
    window.addEventListener("resize", updateDimensions)

    return () => {
      window.removeEventListener("resize", updateDimensions)
    }
  }, [])

  // Animate circles
  useEffect(() => {
    if (dimensions.width === 0 || dimensions.height === 0) return

    const animationFrame = requestAnimationFrame(function animate() {
      setPositions((prevPositions) =>
        prevPositions.map((circle) => {
          // Calculate new position
          let newX = circle.x + circle.speedX
          let newY = circle.y + circle.speedY

          // Bounce off edges
          if (newX <= 0 || newX >= dimensions.width - circle.size) {
            circle.speedX *= -1
            newX = circle.x + circle.speedX
          }

          if (newY <= 0 || newY >= dimensions.height - circle.size) {
            circle.speedY *= -1
            newY = circle.y + circle.speedY
          }

          return {
            ...circle,
            x: newX,
            y: newY,
          }
        }),
      )

      requestAnimationFrame(animate)
    })

    return () => cancelAnimationFrame(animationFrame)
  }, [dimensions])

  return (
    <div
      ref={containerRef}
      className="fixed inset-0 w-full h-full overflow-hidden pointer-events-none z-[-1]"
      style={{ backdropFilter: "blur(1px)" }}
    >
      {positions.map((circle, index) => (
        <div
          key={index}
          className={`absolute rounded-full blur-3xl`}
          style={{
            width: `${circle.size}px`,
            height: `${circle.size}px`,
            left: `${circle.x}px`,
            top: `${circle.y}px`,
            transition: "left 0.5s linear, top 0.5s linear",
            backgroundColor: circle.color,
          }}
        />
      ))}
      {children}
    </div>
  )
}
