import { ObjectId } from 'mongoose';

// Base interfaces
export interface RecipeIngredient {
  name: string;
  quantity: number;
  unit: string;
}

export interface RecipeBase {
  title: string;
  ingredients: RecipeIngredient[];
  steps: string[];
  source: 'AI' | 'DATABASE' | 'USER';
  isLiked: boolean;
}

export interface Recipe extends RecipeBase {
  id: string;
  userId?: string;
  ingredientHash: string;
  createdAt: Date;
  updatedAt: Date;
  ingredientCount: number;
  stepCount: number;
}

// DTOs for API requests
export interface GenerateRecipeDto {
  fridgeItemIds: string[];
  preferences?: {
    cuisine?: string;
    difficulty?: 'easy' | 'medium' | 'hard';
    cookingTime?: number; // in minutes
    servings?: number;
  };
}

export interface CreateRecipeDto {
  title: string;
  ingredients: RecipeIngredient[];
  steps: string[];
  source?: 'USER';
}

export interface UpdateRecipeDto {
  title?: string;
  ingredients?: RecipeIngredient[];
  steps?: string[];
  isLiked?: boolean;
}

// DTOs for API responses
export interface RecipeResponseDto {
  id: string;
  title: string;
  ingredients: RecipeIngredient[];
  steps: string[];
  userId?: string;
  source: string;
  isLiked: boolean;
  ingredientHash: string;
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  ingredientCount: number;
  stepCount: number;
}

export interface RecipesListResponseDto {
  success: boolean;
  data: RecipeResponseDto[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface RecipeResponseSingleDto {
  success: boolean;
  data: RecipeResponseDto;
  message?: string;
}

export interface GenerateRecipeResponseDto {
  success: boolean;
  data: RecipeResponseDto;
  message?: string;
  usageInfo: {
    dailyUsed: number;
    dailyLimit: number;
    remainingGenerations: number;
  };
}

// Query parameters for listing recipes
export interface RecipesQueryParams {
  page?: number;
  limit?: number;
  sortBy?: 'createdAt' | 'title' | 'ingredientCount';
  sortOrder?: 'asc' | 'desc';
  source?: 'AI' | 'DATABASE' | 'USER';
  isLiked?: boolean;
  search?: string;
  dateFrom?: string; // ISO date string
  dateTo?: string; // ISO date string
}

// Recipe filters
export interface RecipeFilters {
  source?: 'AI' | 'DATABASE' | 'USER';
  isLiked?: boolean;
  dateRange?: {
    from: Date;
    to: Date;
  };
  search?: string;
}

// User recipe usage types
export interface UserRecipeUsage {
  id: string;
  userId: string;
  date: Date;
  generatedCount: number;
  userTier: 'FREE' | 'PRO';
  dailyLimit: number;
  remainingGenerations: number;
  usagePercentage: number;
  canGenerateMore: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface DailyLimitResponseDto {
  success: boolean;
  data: {
    dailyUsed: number;
    dailyLimit: number;
    remainingGenerations: number;
    usagePercentage: number;
    canGenerateMore: boolean;
    userTier: 'FREE' | 'PRO';
    resetTime: string; // ISO date string for next reset
  };
}

// AI Service integration types
export interface AIRecipeRequest {
  ingredients: string[];
  preferences?: {
    cuisine?: string;
    difficulty?: 'easy' | 'medium' | 'hard';
    cookingTime?: number;
    servings?: number;
  };
}

export interface AIRecipeResponse {
  title: string;
  ingredients: RecipeIngredient[];
  steps: string[];
  estimatedCookingTime?: number;
  servings?: number;
  difficulty?: string;
  cuisine?: string;
}

// Validation schemas
export interface RecipeValidationRules {
  title: {
    required: true;
    type: 'string';
    minLength: 1;
    maxLength: 200;
    trim: true;
  };
  ingredients: {
    required: true;
    type: 'array';
    minItems: 1;
    items: {
      name: { required: true; type: 'string'; trim: true };
      quantity: { required: true; type: 'number'; min: 0 };
      unit: { required: true; type: 'string'; trim: true };
    };
  };
  steps: {
    required: true;
    type: 'array';
    minItems: 1;
    items: { type: 'string'; minLength: 1; trim: true };
  };
  source: {
    required: false;
    type: 'string';
    enum: ['AI', 'DATABASE', 'USER'];
    default: 'USER';
  };
}

// Error types
export interface RecipeError {
  field: string;
  message: string;
  code: string;
}

export interface RecipeValidationError {
  success: false;
  statusCode: 400;
  message: string;
  errors: RecipeError[];
  timestamp: string;
}

export interface RecipeLimitError {
  success: false;
  statusCode: 429;
  message: string;
  details: {
    dailyUsed: number;
    dailyLimit: number;
    resetTime: string;
  };
  timestamp: string;
}
