"use client"

import { useEffect, useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { X } from "lucide-react"
import Image from "next/image"

interface Ingredient {
  id: string
  name: string
}

interface IngredientsListProps {
  initialIngredients: Ingredient[]
  clickRefill: () => void
  clickStats: () => void
}

export default function IngredientsList({ initialIngredients, clickRefill, clickStats }: IngredientsListProps) {
  const [ingredients, setIngredients] = useState<Ingredient[]>(initialIngredients)

  const handleDelete = (id: string) => {
    setIngredients(ingredients.filter((ingredient) => ingredient.id !== id))
  }

  useEffect(() => {
    setIngredients(initialIngredients)
  }, [initialIngredients])

  return (
    <div className="w-full h-full max-w-md mx-auto">
      {/* Title */}
      <div
        className="mb-4 w-full px-2"
        style={{transition: "opacity 0.3s ease-in-out"}}
      >
        <div className="flex items-center justify-between mb-2">
          <div>
            <h2 className='text-xl font-semibold text-zinc-800 dark:text-zinc-400'>
              What&apos;s in your fridge?
            </h2>

            <p className='text-zinc-600 dark:text-zinc-400'>
              Let&apos;s have a {' '}
              <span className='font-medium text-zinc-900 dark:text-zinc-700'>
                👀
              </span>{' '}
              at your supplies
            </p>
          </div>
          
          <div className="flex items-center gap-1 justify-center w-auto h-10 rounded-full bg-white border border-gray-200 shadow-md cursor-pointer hover:bg-gray-50 transition-colors duration-200 px-3" style={{fontSize: "0.750rem"}} onClick={clickStats}>
            {/* Stats */}
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="#00000070" className="size-6">
              <path strokeLinecap="round" strokeLinejoin="round" d="m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z" />
            </svg>
          </div>
        </div>
      </div>
      
      {/* Items List  */}
      <AnimatePresence>
        {ingredients.map((ingredient, index) => (
          <motion.div
            key={ingredient.id}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, x: -100 }}
            transition={{ duration: 0.2 }}
            className={`
              flex items-center justify-between px-4 py-3 mb-px bg-white border border-gray-200
              ${index === 0 ? "rounded-t-lg" : ""}
              ${index === ingredients.length - 1 ? "rounded-b-lg" : ""}
            `}
          >
            <span className="text-gray-800 font-medium">{ingredient.name}</span>
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => handleDelete(ingredient.id)}
              className="text-gray-400 hover:text-red-500 transition-colors duration-200 focus:outline-none"
              aria-label={`Delete ${ingredient.name}`}
            >
              <X size={18} />
            </motion.button>
          </motion.div>
        ))}
      </AnimatePresence>

      {/* No items fallback  */}
      {ingredients.length === 0 && (
        <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="py-6 text-gray-500 flex flex-col items-center justify-center">
          <Image
            src="/images/leftover-chef_basket.png"
            alt="Leftover Chef Logo"
            width={250}
            height={450}
            className="mt-26"
          />
          <div
            className="mt-4 flex items-center justify-center flex-col"
          >
            <p>
              It looks like your fridge is empty!
            </p>
            <span className="font-semibold text-gray-800 border-[1px] border-[#00000080] px-6 py-2 rounded-4xl" onClick={clickRefill}>Let&apos;s refill it!</span>
          </div>
        </motion.div>
      )}
    </div>
  )
}
