"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkSubscriptionStatus = void 0;
const tslib_1 = require("tslib");
const schemas_1 = require("../database/schemas");
const axios_1 = tslib_1.__importDefault(require("axios"));
const checkSubscriptionStatus = (appUserId) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const account = yield schemas_1.Account.findById(appUserId).select('finances.subscription_plan finances.subscription_expiry');
        if (!account) {
            return {
                success: false,
                message: 'Account with that id does not exist'
            };
        }
        const response = yield axios_1.default.get(`https://api.revenuecat.com/v1/subscribers/${encodeURIComponent(appUserId)}`, {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${process.env.REVENUECAT_API_KEY}`,
            },
        });
        const customerInfo = response.data.subscriber;
        const hazelChefPro = customerInfo.entitlements['HazelChef PRO'];
        if (hazelChefPro) {
            /*
              User has entitlements, check for expiry
            */
            const expiresDate = hazelChefPro.expires_date && new Date(hazelChefPro.expires_date);
            //is currently (from API data - RevenueCat) active?
            const revenueCatStatus = isSubscriptionActive(expiresDate ? new Date(expiresDate) : new Date());
            const currentSubscriptionStatus = isSubscriptionActive(account.finances.subscription_expiry ? new Date(account.finances.subscription_expiry) : new Date());
            //console.log(revenueCatStatus)
            //console.log(currentSubscriptionStatus) 
            if (currentSubscriptionStatus.isPro != revenueCatStatus.isPro) {
                //If the current status coming live from revenue cat is different to whats saved on db, then update
                if (expiresDate instanceof Date && !isNaN(expiresDate.getTime())) {
                    account.finances.subscription_expiry = expiresDate;
                }
            }
            const allowedPlans = ['free', 'hazel_c_01', 'hazel_c_12'];
            if (allowedPlans.includes(hazelChefPro.product_identifier)) {
                account.finances.subscription_plan = hazelChefPro.product_identifier;
            }
            yield account.save();
            return {
                isPro: revenueCatStatus.isPro,
                productIdentifier: revenueCatStatus.isPro ? hazelChefPro.product_identifier : 'free'
            };
        }
        else {
            //No entitlement User is on free plan
            account.finances.subscription_plan = 'free';
            yield account.save();
            return {
                isPro: false,
                productIdentifier: 'free'
            };
        }
    }
    catch (error) {
        const axiosError = error;
        console.error('Error checking subscription status:', axiosError.message);
        /* if (axiosError.response) {
          console.error('Response data:', axiosError.response.data);
          console.error('Response status:', axiosError.response.status);
        } */
        return { isPro: false, productIdentifier: 'free' };
    }
});
exports.checkSubscriptionStatus = checkSubscriptionStatus;
//Check subscription status based on expiry date (compare with current date)
const isSubscriptionActive = (subscription_expiry) => {
    // Get current UTC/Zulu date
    const currentDate = new Date();
    // Compare expiry date with current date
    if (subscription_expiry > currentDate) {
        return {
            success: true,
            isPro: true,
        };
    }
    return {
        success: false,
        message: 'Subscription has expired',
        isPro: false,
    };
};
