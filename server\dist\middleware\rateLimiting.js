"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createCustomRateLimit = exports.authRateLimit = exports.fridgeOperationsRateLimit = exports.recipeGenerationRateLimit = exports.generalRateLimit = void 0;
const tslib_1 = require("tslib");
const express_rate_limit_1 = tslib_1.__importDefault(require("express-rate-limit"));
// Import ipKeyGenerator and type it properly to avoid TS errors
const rateLimit_ = require('express-rate-limit');
const ipKeyGenerator = rateLimit_.ipKeyGenerator;
// General API rate limiting
exports.generalRateLimit = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: (req) => {
        var _a, _b;
        // Check if user is authenticated and get their tier
        const authReq = req;
        if ((_b = (_a = authReq.user) === null || _a === void 0 ? void 0 : _a.finances) === null || _b === void 0 ? void 0 : _b.subscription_plan) {
            const userTier = authReq.user.finances.subscription_plan === 'free' ? 'FREE' : 'PRO';
            return userTier === 'PRO' ? 200 : 100;
        }
        return 50; // Default for unauthenticated users
    },
    message: (req) => {
        var _a, _b;
        const authReq = req;
        let limit = 50; // Default for unauthenticated users
        if ((_b = (_a = authReq.user) === null || _a === void 0 ? void 0 : _a.finances) === null || _b === void 0 ? void 0 : _b.subscription_plan) {
            const userTier = authReq.user.finances.subscription_plan === 'free' ? 'FREE' : 'PRO';
            limit = userTier === 'PRO' ? 200 : 100;
        }
        return {
            success: false,
            statusCode: 429,
            message: 'Too many requests from this IP, please try again later',
            details: {
                limit,
                windowMs: 15 * 60 * 1000,
                retryAfter: Math.round(15 * 60) // seconds
            },
            timestamp: new Date().toISOString()
        };
    },
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req) => {
        var _a, _b;
        const authReq = req;
        return (_b = (_a = authReq.user) === null || _a === void 0 ? void 0 : _a._id) !== null && _b !== void 0 ? _b : ipKeyGenerator(req);
    }
});
// Recipe generation specific rate limiting
exports.recipeGenerationRateLimit = (0, express_rate_limit_1.default)({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 10, // 10 recipe generation requests per hour per user
    message: {
        success: false,
        statusCode: 429,
        message: 'Too many recipe generation requests, please try again later',
        details: {
            limit: 10,
            windowMs: 60 * 60 * 1000,
            retryAfter: Math.round(60 * 60) // seconds
        },
        timestamp: new Date().toISOString()
    },
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req) => {
        var _a, _b;
        const authReq = req;
        const id = (_b = (_a = authReq.user) === null || _a === void 0 ? void 0 : _a._id) !== null && _b !== void 0 ? _b : ipKeyGenerator(req);
        return `recipe_gen_${id}`;
    },
    skip: (req) => {
        var _a, _b;
        // Skip rate limiting for PRO users (they have daily limits instead)
        const authReq = req;
        const userTier = ((_b = (_a = authReq.user) === null || _a === void 0 ? void 0 : _a.finances) === null || _b === void 0 ? void 0 : _b.subscription_plan) === 'free' ? 'FREE' : 'PRO';
        return userTier === 'PRO';
    }
});
// Fridge operations rate limiting (more lenient)
exports.fridgeOperationsRateLimit = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 150, // 150 fridge operations per 15 minutes
    message: {
        success: false,
        statusCode: 429,
        message: 'Too many fridge operations, please try again later',
        details: {
            limit: 150,
            windowMs: 15 * 60 * 1000,
            retryAfter: Math.round(15 * 60) // seconds
        },
        timestamp: new Date().toISOString()
    },
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req) => {
        var _a, _b;
        const authReq = req;
        const id = (_b = (_a = authReq.user) === null || _a === void 0 ? void 0 : _a._id) !== null && _b !== void 0 ? _b : ipKeyGenerator(req);
        return `fridge_ops_${id}`;
    }
});
// Authentication rate limiting (for login attempts)
exports.authRateLimit = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // 5 login attempts per 15 minutes per IP
    message: {
        success: false,
        statusCode: 429,
        message: 'Too many authentication attempts, please try again later',
        details: {
            limit: 5,
            windowMs: 15 * 60 * 1000,
            retryAfter: Math.round(15 * 60) // seconds
        },
        timestamp: new Date().toISOString()
    },
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req) => `auth_${ipKeyGenerator(req)}`
});
// Custom rate limit handler for specific endpoints
const createCustomRateLimit = (options) => {
    return (0, express_rate_limit_1.default)({
        windowMs: options.windowMs,
        max: options.max,
        message: {
            success: false,
            statusCode: 429,
            message: options.message,
            details: {
                limit: options.max,
                windowMs: options.windowMs,
                retryAfter: Math.round(options.windowMs / 1000)
            },
            timestamp: new Date().toISOString()
        },
        standardHeaders: true,
        legacyHeaders: false,
        keyGenerator: (req) => {
            var _a, _b;
            const authReq = req;
            const prefix = options.keyPrefix || 'custom';
            const id = (_b = (_a = authReq.user) === null || _a === void 0 ? void 0 : _a._id) !== null && _b !== void 0 ? _b : ipKeyGenerator(req);
            return `${prefix}_${id}`;
        }
    });
};
exports.createCustomRateLimit = createCustomRateLimit;
