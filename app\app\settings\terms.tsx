import React, { useState } from 'react'
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import { Ionicons } from "@expo/vector-icons"

import Page from '@/components/templates/Page'
import { ScrollView } from 'react-native-gesture-handler'
import Header from '@/components/templates/Header'

interface Section {
  id: string
  title: string
  content: string[]
  expanded?: boolean
}

const termsData: Section[] = [
  {
    id: "acceptance",
    title: "1. Acceptance of Terms",
    content: [
      "By downloading, installing, or using the Hazel mobile application ('App'), you agree to be bound by these Terms and Conditions ('Terms').",
      "If you do not agree to these Terms, please do not use the App.",
      "We reserve the right to modify these Terms at any time. Continued use of the App constitutes acceptance of any changes.",
    ],
  },
  {
    id: "description",
    title: "2. App Description and Services",
    content: [
      "Hazel is a food management application that helps users track food items, monitor expiration dates, plan meals, and reduce food waste.",
      "The App provides features including but not limited to: food inventory tracking, expiration notifications, recipe suggestions, meal planning, and shopping lists.",
      "Premium features may require a paid subscription and include advanced functionality such as barcode scanning, unlimited items, and family sharing.",
    ],
  },
  {
    id: "user-accounts",
    title: "3. User Accounts and Registration",
    content: [
      "You must create an account to use certain features of the App. You are responsible for maintaining the confidentiality of your account credentials.",
      "You must provide accurate, current, and complete information during registration and keep your account information updated.",
      "You are responsible for all activities that occur under your account. Notify us immediately of any unauthorized use.",
      "You must be at least 13 years old to create an account. Users under 18 must have parental consent.",
    ],
  },
  {
    id: "acceptable-use",
    title: "4. Acceptable Use Policy",
    content: [
      "You may use the App only for lawful purposes and in accordance with these Terms.",
      "You agree not to use the App to: violate any laws, infringe on intellectual property rights, transmit harmful code, or engage in fraudulent activities.",
      "You may not attempt to reverse engineer, decompile, or disassemble the App or any part thereof.",
      "Commercial use of the App without our express written consent is prohibited.",
    ],
  },
  {
    id: "subscriptions",
    title: "5. Subscriptions and Billing",
    content: [
      "Premium subscriptions are billed on a recurring basis (monthly or annually) until cancelled.",
      "Payment will be charged to your App Store or Google Play account at confirmation of purchase.",
      "Subscriptions automatically renew unless auto-renewal is turned off at least 24 hours before the end of the current period.",
      "You may cancel your subscription at any time through your device's subscription settings. Cancellation takes effect at the end of the current billing period.",
      "No refunds are provided for unused portions of subscription periods, except as required by law.",
    ],
  },
  {
    id: "food-safety",
    title: "6. Food Safety Disclaimer",
    content: [
      "The App provides general information about food storage and expiration dates for informational purposes only.",
      "We do not guarantee the accuracy of expiration date calculations or food safety recommendations.",
      "You are solely responsible for determining the safety and quality of food items. Always use your judgment and follow proper food safety guidelines.",
      "The App is not a substitute for professional food safety advice. When in doubt, discard questionable food items.",
      "We are not liable for any illness, injury, or damage resulting from food consumption decisions made using the App.",
    ],
  },
  {
    id: "user-content",
    title: "7. User-Generated Content",
    content: [
      "You retain ownership of content you create or upload to the App, including photos, notes, and custom recipes.",
      "By uploading content, you grant us a non-exclusive, royalty-free license to use, display, and distribute your content within the App.",
      "You represent that you have the right to upload any content and that it does not violate any third-party rights.",
      "We reserve the right to remove any content that violates these Terms or is otherwise objectionable.",
    ],
  },
  {
    id: "privacy",
    title: "8. Privacy and Data Protection",
    content: [
      "Your privacy is important to us. Please review our Privacy Policy, which explains how we collect, use, and protect your information.",
      "We may collect usage data, device information, and user-provided content to improve the App and provide personalized features.",
      "We implement appropriate security measures to protect your personal information, but cannot guarantee absolute security.",
      "You may request deletion of your account and associated data at any time through the App settings.",
    ],
  },
  {
    id: "intellectual-property",
    title: "9. Intellectual Property Rights",
    content: [
      "The App and its original content, features, and functionality are owned by Hazel and are protected by international copyright, trademark, and other intellectual property laws.",
      "Our trademarks and trade dress may not be used without our prior written consent.",
      "You may not copy, modify, distribute, sell, or lease any part of the App or its content.",
    ],
  },
  {
    id: "limitation-liability",
    title: "10. Limitation of Liability",
    content: [
      "To the maximum extent permitted by law, Hazel shall not be liable for any indirect, incidental, special, consequential, or punitive damages.",
      "Our total liability for any claims related to the App shall not exceed the amount you paid for the App in the 12 months preceding the claim.",
      "Some jurisdictions do not allow the exclusion of certain warranties or limitation of liability, so these limitations may not apply to you.",
    ],
  },
  {
    id: "termination",
    title: "11. Termination",
    content: [
      "You may terminate your account at any time by deleting the App and contacting us to request account deletion.",
      "We may terminate or suspend your account immediately, without prior notice, for conduct that violates these Terms.",
      "Upon termination, your right to use the App ceases immediately, and we may delete your account and data.",
    ],
  },
  {
    id: "governing-law",
    title: "12. Governing Law and Disputes",
    content: [
      "These Terms shall be governed by and construed in accordance with the laws of Delaware, United States, without regard to conflict of law principles.",
      "Any disputes arising from these Terms or the App shall be resolved through binding arbitration, except where prohibited by law.",
      "You agree to resolve disputes individually and waive any right to participate in class action lawsuits.",
    ],
  },
  {
    id: "contact",
    title: "13. Contact Information",
    content: [
      "If you have any questions about these Terms and Conditions, please contact us",
    ],
  },
]


const Index = () => {

  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set())

  const toggleSection = (sectionId: string) => {
    const newExpanded = new Set(expandedSections)
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId)
    } else {
      newExpanded.add(sectionId)
    }
    setExpandedSections(newExpanded)
  }

  const renderSection = (section: Section) => {
    const isExpanded = expandedSections.has(section.id)

    return (
      <View key={section.id} style={styles.sectionCard}>
        <TouchableOpacity style={styles.sectionHeader} onPress={() => toggleSection(section.id)}>
          <Text style={styles.sectionTitle}>{section.title}</Text>
          <Ionicons
            name={isExpanded ? "chevron-up" : "chevron-down"}
            size={20}
            color="#666"
            style={styles.chevronIcon}
          />
        </TouchableOpacity>

        {isExpanded && (
          <View style={styles.sectionContent}>
            {section.content.map((paragraph, index) => (
              <Text key={index} style={styles.paragraphText}>
                {paragraph}
              </Text>
            ))}
          </View>
        )}
      </View>
    )
  }


  return (
    <Page noPaddingTop alignItems='center' justifyContent='space-between' >
      <Header buttonBack text=' ' />
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Introduction */}
        <View style={styles.introSection}>
          <View style={styles.privacyIcon}>
            <Ionicons name="document" size={32} color="rgba(34,197,94,0.7)" />
          </View>
          <Text style={styles.introTitle}>Terms & Conditions</Text>
          <Text style={styles.introSubtitle}>Last updated: May 2025</Text>
          <Text style={styles.introText}>
            Please read these Terms and Conditions carefully before using the Hazel application. These terms govern your
            use of our service and outline your rights and responsibilities as a user.
          </Text>
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => {
              const allSectionIds = new Set(termsData.map((section) => section.id))
              setExpandedSections(allSectionIds)
            }}
          >
            <Ionicons name="expand" size={16} color="rgba(34,197,94,0.7)" />
            <Text style={styles.actionButtonText}>Expand All</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionButton} onPress={() => setExpandedSections(new Set())}>
            <Ionicons name="contract" size={16} color="rgba(34,197,94,0.7)" />
            <Text style={styles.actionButtonText}>Collapse All</Text>
          </TouchableOpacity>
        </View>

        {/* Terms Sections */}
        <View style={styles.sectionsContainer}>{termsData.map(renderSection)}</View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            By continuing to use Hazel, you acknowledge that you have read, understood, and agree to be bound by these
            Terms and Conditions.
          </Text>
          <TouchableOpacity style={styles.contactButton}>
            <Ionicons name="mail" size={16} color="rgba(34,197,94,0.7)" />
            <Text style={styles.contactButtonText}>Contact Legal Team</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

    </Page>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e9ecef",
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: "600",
    textAlign: "center",
    color: "#333",
  },
  headerSpacer: {
    width: 32,
  },
  scrollView: {
    flex: 1,
  },
  introSection: {
    padding: 24,
    backgroundColor: "#fff",
    marginBottom: 16,
    alignItems: "center",
    marginTop: 100
  },
  privacyIcon: {
    marginBottom: 16,
  },
  introTitle: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 8,
  },
  introSubtitle: {
    fontSize: 14,
    color: "rgba(34,197,94,0.7)",
    fontWeight: "600",
    marginBottom: 16,
  },
  introText: {
    fontSize: 16,
    color: "#666",
    lineHeight: 24,
    textAlign: "center",
    marginTop: 50
  },
  highlightsSection: {
    backgroundColor: "#fff",
    marginHorizontal: 20,
    marginBottom: 16,
    padding: 20,
    borderRadius: 12,
  },
  highlightsTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    marginBottom: 16,
  },
  highlightItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
    gap: 12,
  },
  highlightText: {
    fontSize: 14,
    color: "#555",
    flex: 1,
  },
  quickActions: {
    flexDirection: "row",
    paddingHorizontal: 20,
    marginBottom: 16,
    gap: 12,
  },
  actionButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#fff",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: "rgba(34,197,94,0.7)",
    gap: 6,
  },
  actionButtonText: {
    fontSize: 14,
    color: "rgba(34,197,94,0.7)",
    fontWeight: "500",
  },
  sectionsContainer: {
    paddingHorizontal: 20,
    gap: 12,
  },
  sectionCard: {
    backgroundColor: "#fff",
    borderRadius: 12,
    overflow: "hidden",
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    flex: 1,
    marginRight: 12,
  },
  chevronIcon: {
    marginLeft: 8,
  },
  sectionContent: {
    paddingHorizontal: 20,
    paddingTop: 10,
    borderTopWidth: 1,
    borderTopColor: "#f1f3f4",
  },
  paragraphText: {
    fontSize: 14,
    color: "#555",
    lineHeight: 20,
    marginBottom: 12,
  },
  subsection: {
    marginTop: 16,
    marginBottom: 8,
  },
  subsectionTitle: {
    fontSize: 15,
    fontWeight: "600",
    color: "#333",
    marginBottom: 8,
  },
  bulletPoint: {
    flexDirection: "row",
    marginBottom: 6,
    paddingLeft: 8,
  },
  bulletText: {
    fontSize: 14,
    color: "rgba(34,197,94,0.7)",
    marginRight: 8,
    fontWeight: "bold",
  },
  bulletContent: {
    fontSize: 14,
    color: "#555",
    lineHeight: 18,
    flex: 1,
  },
  footer: {
    padding: 24,
    backgroundColor: "#fff",
    marginTop: 16,
    marginBottom: 32,
  },
  footerText: {
    fontSize: 14,
    color: "#666",
    lineHeight: 20,
    marginBottom: 16,
    textAlign: "center",
  },
  contactButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#00000010",
    paddingVertical: 20,
    paddingHorizontal: 20,
    borderRadius: 8,
    gap: 8,
    marginBottom: 12,
  },
  contactButtonText: {
    fontSize: 14,
    color: "rgba(34,197,94,0.7)",
    fontWeight: "500",
  },
  settingsButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "rgba(34,197,94,0.7)",
    paddingVertical: 20,
    paddingHorizontal: 20,
    borderRadius: 8,
    gap: 8,
  },
  settingsButtonText: {
    fontSize: 14,
    color: "#fff",
    fontWeight: "500",
  },
})



export default Index

