"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const middleware_1 = require("../middleware");
const recipe_1 = require("../controllers/recipe");
const router = (0, express_1.Router)();
// Apply authentication middleware to all routes
router.use(middleware_1.AuthenticateTokenOAuth);
// GET /recipes - Get user's recipes with filtering
router.get('/', middleware_1.generalRateLimit, middleware_1.validateRecipesQuery, middleware_1.validatePagination, recipe_1.getRecipes);
// POST /recipes/generate - Generate recipe from selected fridge items
router.post('/generate', middleware_1.recipeGenerationRateLimit, middleware_1.validateGenerateRecipe, recipe_1.generateRecipe);
// POST /recipes/:id/like - Toggle like status
router.post('/:id/like', middleware_1.generalRateLimit, middleware_1.validateMongoId, recipe_1.likeRecipe);
// GET /recipes/daily-limit - Get current usage vs limit
router.get('/daily-limit', middleware_1.generalRateLimit, recipe_1.getDailyLimit);
exports.default = router;
