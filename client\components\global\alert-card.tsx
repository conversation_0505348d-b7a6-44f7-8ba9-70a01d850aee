import type { ReactNode } from "react"
import { AlertCircle, AlertTriangle, CheckCircle, Info } from "lucide-react"

type AlertVariant = "error" | "warning" | "info" | "success"

interface AlertCardProps {
    title: string
    description?: string
    icon?: ReactNode
    variant?: AlertVariant
    className?: string
}

export default function AlertCard({ title, description, icon, variant = "info", className = "" }: AlertCardProps) {
    // Define variant-specific styles
    const variantStyles: Record<
        AlertVariant,
        {
            containerBorder: string
            containerBg: string
            iconBg: string
            iconColor: string
            titleColor: string
        }
    > = {
        error: {
            containerBorder: "border-red-200",
            containerBg: "bg-red-50",
            iconBg: "bg-red-100",
            iconColor: "text-red-600",
            titleColor: "text-red-800",
        },
        warning: {
            containerBorder: "border-amber-200",
            containerBg: "bg-amber-50",
            iconBg: "bg-amber-100",
            iconColor: "text-amber-600",
            titleColor: "text-amber-800",
        },
        info: {
            containerBorder: "border-blue-200",
            containerBg: "bg-blue-50",
            iconBg: "bg-blue-100",
            iconColor: "text-blue-600",
            titleColor: "text-blue-800",
        },
        success: {
            containerBorder: "border-green-200",
            containerBg: "bg-green-50",
            iconBg: "bg-green-100",
            iconColor: "text-green-600",
            titleColor: "text-green-800",
        },
    }

    const styles = variantStyles[variant]

    // Default icons based on variant
    const defaultIcons: Record<AlertVariant, ReactNode> = {
        error: <AlertCircle className={`h-5 w-5 ${styles.iconColor}`} />,
        warning: <AlertTriangle className={`h-5 w-5 ${styles.iconColor}`} />,
        info: <Info className={`h-5 w-5 ${styles.iconColor}`} />,
        success: <CheckCircle className={`h-5 w-5 ${styles.iconColor}`} />,
    }

    const displayIcon = icon || defaultIcons[variant]

    return (
        <div className={`rounded-lg border ${styles.containerBorder} ${styles.containerBg} p-3 ${className}`}>
            <div className="flex items-start">
                <div className={`mr-3 flex-shrink-0 ${styles.iconBg} p-2 rounded-full`}>{displayIcon}</div>
                <div>
                    <h3 className={`text-[13px] font-medium ${styles.titleColor}`}>{title}</h3>
                    {description && <div className="mt-1 text-[12px] text-gray-500">{description}</div>}
                </div>
            </div>
        </div>
    )
}
