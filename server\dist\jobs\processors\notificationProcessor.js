"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.processPushNotification = void 0;
const tslib_1 = require("tslib");
const schemas_1 = require("../../database/schemas");
/**
 * Process push notification jobs
 */
const processPushNotification = (job) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    var _a;
    const { userId, type, title, body, data } = job.data;
    try {
        console.log(`Processing push notification for user ${userId}, type: ${type}`);
        // Get user's push token
        const user = yield schemas_1.Account.findById(userId).select('notifications.expo_push_token').lean();
        if (!user || !((_a = user.notifications) === null || _a === void 0 ? void 0 : _a.expo_push_token)) {
            console.log(`User ${userId} has no push token, skipping notification`);
            return { success: false, reason: 'No push token' };
        }
        const pushToken = user.notifications.expo_push_token;
        // Validate Expo push token format
        if (!pushToken.startsWith('ExponentPushToken[') && !pushToken.startsWith('ExpoPushToken[')) {
            console.log(`Invalid push token format for user ${userId}`);
            return { success: false, reason: 'Invalid push token format' };
        }
        // Prepare notification payload
        const notificationPayload = {
            to: pushToken,
            title,
            body,
            data: Object.assign({ type,
                userId, timestamp: new Date().toISOString() }, data),
            sound: 'default',
            badge: 1,
            priority: 'high',
            channelId: 'recipe-notifications'
        };
        // Send notification using Expo Push API
        const response = yield sendExpoNotification(notificationPayload);
        if (response.success) {
            console.log(`Push notification sent successfully to user ${userId}`);
            return {
                success: true,
                pushToken,
                receiptId: response.receiptId,
                type
            };
        }
        else {
            console.error(`Failed to send push notification to user ${userId}:`, response.error);
            return {
                success: false,
                reason: response.error,
                pushToken
            };
        }
    }
    catch (error) {
        console.error(`Push notification job failed for user ${userId}:`, error.message);
        throw error;
    }
});
exports.processPushNotification = processPushNotification;
/**
 * Send notification using Expo Push API
 */
function sendExpoNotification(payload) {
    return tslib_1.__awaiter(this, void 0, void 0, function* () {
        try {
            // TODO:  Expo SDK or make HTTP requests to Expo's API
            // For now, we'll simulate the API call
            console.log('Sending Expo notification:', {
                to: payload.to,
                title: payload.title,
                body: payload.body,
                data: payload.data
            });
            // Simulate API response
            const mockReceiptId = `receipt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            // Simulate occasional failures for testing
            if (Math.random() < 0.05) { // 5% failure rate
                return {
                    success: false,
                    error: 'Simulated API failure'
                };
            }
            return {
                success: true,
                receiptId: mockReceiptId
            };
            /*
            // Real implementation for expo:
            
            const fetch = require('node-fetch');
            
            const response = await fetch('https://exp.host/--/api/v2/push/send', {
              method: 'POST',
              headers: {
                'Accept': 'application/json',
                'Accept-encoding': 'gzip, deflate',
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(payload)
            });
        
            const result = await response.json();
            
            if (result.data && result.data[0]) {
              const ticketData = result.data[0];
              if (ticketData.status === 'ok') {
                return {
                  success: true,
                  receiptId: ticketData.id
                };
              } else {
                return {
                  success: false,
                  error: ticketData.message || 'Unknown error'
                };
              }
            }
        
            return {
              success: false,
              error: 'Invalid response format'
            };
            */
        }
        catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    });
}
/**
 * Send email using configured email service
 */
function sendEmail(emailContent) {
    return tslib_1.__awaiter(this, void 0, void 0, function* () {
        try {
            //TODO : Implement mailing service
            console.log('Sending email:', {
                to: emailContent.to,
                subject: emailContent.subject
            });
            // Simulate email sending
            const mockMessageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            return {
                success: true,
                messageId: mockMessageId
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    });
}
