import React from 'react'

interface ButtonProps {
    children: React.ReactNode
    variant: 'primary' | 'outline'
    button?: {
        color: string
    }
    text?: {
        color: string
        size?: string
    }
    onClick?: () => void
    href?: string
}


function Button({ children, variant, button, text, onClick,href }: ButtonProps) {

    const buttonStyles = {
        primary: {
            
            backgroundColor: button?.color || '#00c951',
            color: text?.color || 'text-white',
            fontSize: text?.size || '14px'
        },
        outline: {
            
            backgroundColor: 'bg-transparent',
            color: text?.color || '#000000',
            fontSize: text?.size || '14px',
            border: `1px solid ${button?.color || '#00000030'}`
        }
    }

   
    if (href) {
        return (
            <a
                href={href}
                className={`flex items-center justify-center rounded-lg py-2 px-6 cursor-pointer`}
                style={variant === 'primary' ? buttonStyles.primary : buttonStyles.outline}
            >
                {children}
            </a>
        )
    }

  return (
    <div
        className={`flex items-center justify-center rounded-lg py-2 px-6 cursor-pointer`}
        style={variant === 'primary' ? buttonStyles.primary : buttonStyles.outline}
        onClick={onClick}
    >
        {children}
    </div>
  )
}

export default Button