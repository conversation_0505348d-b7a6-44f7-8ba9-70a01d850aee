import { Request, Response } from 'express';
import { FridgeService } from '../services';
import {
  CreateFridgeItemDto,
  UpdateFridgeItemDto,
  FridgeItemsQueryParams,
  AuthenticatedRequest
} from '../types';

const fridgeService = new FridgeService();

/**
 * Get all fridge items for authenticated user
 * GET /fridge
 */
export const getFridgeItems = async (req: Request, res: Response): Promise<void> => {
  const authReq = req as AuthenticatedRequest;
  try {
    if (!authReq.user) {
      res.status(401).json({ success: false, message: 'Unauthorized', timestamp: new Date().toISOString() });
      return;
    }
    const userId = authReq.user._id;
    const queryParams: FridgeItemsQueryParams = {
      page: parseInt(req.query.page as string) || 1,
      limit: parseInt(req.query.limit as string) || 20,
      sortBy: req.query.sortBy as any || 'expiryDate',
      sortOrder: req.query.sortOrder as any || 'asc',
      expiryStatus: req.query.expiryStatus as any,
      isFavorite: req.query.isFavorite ? req.query.isFavorite === 'true' : undefined,
      search: req.query.search as string
    };

    const result = await fridgeService.getUserFridgeItems(userId, queryParams);

    if (!result.success) {
      res.status(result.statusCode || 500).json({
        success: false,
        message: result.error,
        timestamp: new Date().toISOString()
      });
      return;
    }

    if (!result.data) {
      res.status(500).json({ success: false, message: 'Missing data', timestamp: new Date().toISOString() });
      return;
    }
    res.status(200).json({
      success: true,
      data: result.data.items,
      pagination: result.data.pagination, timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Create a new fridge item
 * POST /fridge
 */
export const createFridgeItem = async (req: Request, res: Response): Promise<void> => {
  try {
    const authReq = req as AuthenticatedRequest;
    if (!authReq.user) { res.status(401).json({ success: false, message: 'Unauthorized', timestamp: new Date().toISOString() }); return; }
    const userId = authReq.user._id;
    const itemData: CreateFridgeItemDto = req.body;

    // Basic validation
    if (!itemData.name || !itemData.quantity || !itemData.unit || !itemData.expiryDate) {
      res.status(400).json({
        success: false,
        message: 'Missing required fields: name, quantity, unit, expiryDate',
        timestamp: new Date().toISOString()
      });
      return;
    }

    const result = await fridgeService.createFridgeItem(userId, itemData);

    if (!result.success) {
      res.status(result.statusCode || 500).json({
        success: false,
        message: result.error,
        timestamp: new Date().toISOString()
      });
      return;
    }

    res.status(201).json({
      success: true,
      data: result.data,
      message: 'Fridge item created successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Update an existing fridge item
 * PUT /fridge/:id
 */
export const updateFridgeItem = async (req: Request, res: Response): Promise<void> => {
  try {
    const authReq = req as AuthenticatedRequest;
    if (!authReq.user) { res.status(401).json({ success: false, message: 'Unauthorized', timestamp: new Date().toISOString() }); return; }
    const userId = authReq.user._id;
    const itemId = req.params.id;
    const updateData: UpdateFridgeItemDto = req.body;

    if (!itemId) {
      res.status(400).json({
        success: false,
        message: 'Item ID is required',
        timestamp: new Date().toISOString()
      });
      return;
    }

    const result = await fridgeService.updateFridgeItem(userId, itemId, updateData);

    if (!result.success) {
      res.status(result.statusCode || 500).json({
        success: false,
        message: result.error,
        timestamp: new Date().toISOString()
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: result.data,
      message: 'Fridge item updated successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Delete a fridge item
 * DELETE /fridge/:id
 */
export const deleteFridgeItem = async (req: Request, res: Response): Promise<void> => {
  try {
    const authReq = req as AuthenticatedRequest;
    if (!authReq.user) { res.status(401).json({ success: false, message: 'Unauthorized', timestamp: new Date().toISOString() }); return; }
    const userId = authReq.user._id;
    const itemId = req.params.id;

    if (!itemId) {
      res.status(400).json({
        success: false,
        message: 'Item ID is required',
        timestamp: new Date().toISOString()
      });
      return;
    }

    const result = await fridgeService.deleteFridgeItem(userId, itemId);

    if (!result.success) {
      res.status(result.statusCode || 500).json({
        success: false,
        message: result.error,
        timestamp: new Date().toISOString()
      });
      return;
    }

    res.status(200).json({
      success: true,
      message: 'Fridge item deleted successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Toggle favorite status of a fridge item
 * POST /fridge/:id/favorite
 */
export const toggleFavorite = async (req: Request, res: Response): Promise<void> => {
  try {
    const authReq = req as AuthenticatedRequest;
    if (!authReq.user) { res.status(401).json({ success: false, message: 'Unauthorized', timestamp: new Date().toISOString() }); return; }
    const userId = authReq.user._id;
    const itemId = req.params.id;

    if (!itemId) {
      res.status(400).json({
        success: false,
        message: 'Item ID is required',
        timestamp: new Date().toISOString()
      });
      return;
    }

    const result = await fridgeService.toggleFavoriteStatus(userId, itemId);

    if (!result.success) {
      res.status(result.statusCode || 500).json({
        success: false,
        message: result.error,
        timestamp: new Date().toISOString()
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: result.data,
      message: 'Favorite status updated successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get favorite items for authenticated user
 * GET /fridge/favorites
 */
export const getFavoriteItems = async (req: Request, res: Response): Promise<void> => {
  try {
    const authReq = req as AuthenticatedRequest;
    if (!authReq.user) { res.status(401).json({ success: false, message: 'Unauthorized', timestamp: new Date().toISOString() }); return; }
    const userId = authReq.user._id;

    const result = await fridgeService.getFavoriteItems(userId);

    if (!result.success) {
      res.status(result.statusCode || 500).json({
        success: false,
        message: result.error,
        timestamp: new Date().toISOString()
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: result.data,
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get recommended items for authenticated user
 * GET /fridge/recommendations
 */
export const getRecommendations = async (req: Request, res: Response): Promise<void> => {
  try {
    const authReq = req as AuthenticatedRequest;
    if (!authReq.user) { res.status(401).json({ success: false, message: 'Unauthorized', timestamp: new Date().toISOString() }); return; }
    const userId = authReq.user._id;

    const result = await fridgeService.getRecommendedItems(userId);

    if (!result.success) {
      res.status(result.statusCode || 500).json({
        success: false,
        message: result.error,
        timestamp: new Date().toISOString()
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: result.data,
      message: 'Recommendations retrieved successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  }
};
