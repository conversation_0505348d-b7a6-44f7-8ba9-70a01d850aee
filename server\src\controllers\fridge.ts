import { Request, Response } from 'express';
import { FridgeService } from '../services';
import {
  CreateFridgeItemDto,
  UpdateFridgeItemDto,
  FridgeItemsQueryParams,
  AuthenticatedRequest,
  ShelfLifeLookupResponseDto
} from '../types';

const fridgeService = new FridgeService();

/**
 * Get all fridge items for authenticated user
 * GET /fridge
 */
export const getFridgeItems = async (req: Request, res: Response): Promise<void> => {
  const authReq = req as AuthenticatedRequest;
  try {
    if (!authReq.user) {
      res.status(401).json({ success: false, message: 'Unauthorized', timestamp: new Date().toISOString() });
      return;
    }
    const userId = authReq.user._id;
    const queryParams: FridgeItemsQueryParams = {
      page: parseInt(req.query.page as string) || 1,
      limit: parseInt(req.query.limit as string) || 20,
      sortBy: req.query.sortBy as any || 'expiryDate',
      sortOrder: req.query.sortOrder as any || 'asc',
      expiryStatus: req.query.expiryStatus as any,
      isFavorite: req.query.isFavorite ? req.query.isFavorite === 'true' : undefined,
      search: req.query.search as string
    };

    const result = await fridgeService.getUserFridgeItems(userId, queryParams);

    if (!result.success) {
      res.status(result.statusCode || 500).json({
        success: false,
        message: result.error,
        timestamp: new Date().toISOString()
      });
      return;
    }

    if (!result.data) {
      res.status(500).json({ success: false, message: 'Missing data', timestamp: new Date().toISOString() });
      return;
    }
    res.status(200).json({
      success: true,
      data: result.data.items,
      pagination: result.data.pagination, timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Create a new fridge item
 * POST /fridge
 */
export const createFridgeItem = async (req: Request, res: Response): Promise<void> => {
  try {
    const authReq = req as AuthenticatedRequest;
    if (!authReq.user) { res.status(401).json({ success: false, message: 'Unauthorized', timestamp: new Date().toISOString() }); return; }
    const userId = authReq.user._id;
    const itemData: CreateFridgeItemDto = req.body;

    // Basic validation
    if (!itemData.name || !itemData.quantity || !itemData.unit) {
      res.status(400).json({
        success: false,
        message: 'Missing required fields: name, quantity, unit',
        timestamp: new Date().toISOString()
      });
      return;
    }

    const result = await fridgeService.createFridgeItem(userId, itemData);

    if (!result.success) {
      res.status(result.statusCode || 500).json({
        success: false,
        message: result.error,
        timestamp: new Date().toISOString()
      });
      return;
    }

    const message = result.data.expiryCalculated
      ? 'Fridge item created successfully with calculated expiry date'
      : 'Fridge item created successfully';

    res.status(201).json({
      success: true,
      data: result.data,
      message,
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Update an existing fridge item
 * PUT /fridge/:id
 */
export const updateFridgeItem = async (req: Request, res: Response): Promise<void> => {
  try {
    const authReq = req as AuthenticatedRequest;
    if (!authReq.user) { res.status(401).json({ success: false, message: 'Unauthorized', timestamp: new Date().toISOString() }); return; }
    const userId = authReq.user._id;
    const itemId = req.params.id;
    const updateData: UpdateFridgeItemDto = req.body;

    if (!itemId) {
      res.status(400).json({
        success: false,
        message: 'Item ID is required',
        timestamp: new Date().toISOString()
      });
      return;
    }

    const result = await fridgeService.updateFridgeItem(userId, itemId, updateData);

    if (!result.success) {
      res.status(result.statusCode || 500).json({
        success: false,
        message: result.error,
        timestamp: new Date().toISOString()
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: result.data,
      message: 'Fridge item updated successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Delete a fridge item
 * DELETE /fridge/:id
 */
export const deleteFridgeItem = async (req: Request, res: Response): Promise<void> => {
  try {
    const authReq = req as AuthenticatedRequest;
    if (!authReq.user) { res.status(401).json({ success: false, message: 'Unauthorized', timestamp: new Date().toISOString() }); return; }
    const userId = authReq.user._id;
    const itemId = req.params.id;

    if (!itemId) {
      res.status(400).json({
        success: false,
        message: 'Item ID is required',
        timestamp: new Date().toISOString()
      });
      return;
    }

    const result = await fridgeService.deleteFridgeItem(userId, itemId);

    if (!result.success) {
      res.status(result.statusCode || 500).json({
        success: false,
        message: result.error,
        timestamp: new Date().toISOString()
      });
      return;
    }

    res.status(200).json({
      success: true,
      message: 'Fridge item deleted successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Toggle favorite status of a fridge item
 * POST /fridge/:id/favorite
 */
export const toggleFavorite = async (req: Request, res: Response): Promise<void> => {
  try {
    const authReq = req as AuthenticatedRequest;
    if (!authReq.user) { res.status(401).json({ success: false, message: 'Unauthorized', timestamp: new Date().toISOString() }); return; }
    const userId = authReq.user._id;
    const itemId = req.params.id;

    if (!itemId) {
      res.status(400).json({
        success: false,
        message: 'Item ID is required',
        timestamp: new Date().toISOString()
      });
      return;
    }

    const result = await fridgeService.toggleFavoriteStatus(userId, itemId);

    if (!result.success) {
      res.status(result.statusCode || 500).json({
        success: false,
        message: result.error,
        timestamp: new Date().toISOString()
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: result.data,
      message: 'Favorite status updated successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get favorite items for authenticated user
 * GET /fridge/favorites
 */
export const getFavoriteItems = async (req: Request, res: Response): Promise<void> => {
  try {
    const authReq = req as AuthenticatedRequest;
    if (!authReq.user) { res.status(401).json({ success: false, message: 'Unauthorized', timestamp: new Date().toISOString() }); return; }
    const userId = authReq.user._id;

    const result = await fridgeService.getFavoriteItems(userId);

    if (!result.success) {
      res.status(result.statusCode || 500).json({
        success: false,
        message: result.error,
        timestamp: new Date().toISOString()
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: result.data,
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get recommended items for authenticated user
 * GET /fridge/recommendations
 */
export const getRecommendations = async (req: Request, res: Response): Promise<void> => {
  try {
    const authReq = req as AuthenticatedRequest;
    if (!authReq.user) { res.status(401).json({ success: false, message: 'Unauthorized', timestamp: new Date().toISOString() }); return; }
    const userId = authReq.user._id;

    const result = await fridgeService.getRecommendedItems(userId);

    if (!result.success) {
      res.status(result.statusCode || 500).json({
        success: false,
        message: result.error,
        timestamp: new Date().toISOString()
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: result.data,
      message: 'Recommendations retrieved successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get shelf life information for a specific food item
 * GET /fridge/shelf-life/:foodName
 */
export const getShelfLifeInfo = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const foodName = req.params.foodName;

    if (!foodName || foodName.trim().length === 0) {
      res.status(400).json({
        success: false,
        message: 'Food name is required',
        timestamp: new Date().toISOString()
      });
      return;
    }

    const result = await fridgeService.getShelfLifeInfo(foodName.trim());

    if (!result.success) {
      // If not found, try to get similar foods
      const similarResult = await fridgeService.getSimilarFoods(foodName.trim(), 3);

      const response: ShelfLifeLookupResponseDto = {
        success: false,
        message: result.error || 'Shelf life information not found',
        similarFoods: similarResult.success ? similarResult.data : undefined,
        timestamp: new Date().toISOString()
      };

      res.status(result.statusCode || 404).json(response);
      return;
    }

    const response: ShelfLifeLookupResponseDto = {
      success: true,
      data: result.data,
      message: 'Shelf life information retrieved successfully',
      timestamp: new Date().toISOString()
    };

    res.status(200).json(response);
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  }
};
