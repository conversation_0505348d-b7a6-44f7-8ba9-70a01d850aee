"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
const mongoose_1 = tslib_1.__importDefault(require("mongoose"));
const userRecipeUsageSchema = new mongoose_1.default.Schema({
    userId: {
        type: mongoose_1.default.Schema.Types.ObjectId,
        ref: 'Account',
        required: true,
        index: true
    },
    date: {
        type: Date,
        required: [true, 'Date is required'],
        validate: {
            validator: function (value) {
                // Ensure the date is set to start of day (00:00:00)
                const startOfDay = new Date(value);
                startOfDay.setHours(0, 0, 0, 0);
                return value.getTime() === startOfDay.getTime();
            },
            message: 'Date must be set to start of day (00:00:00)'
        }
    },
    generatedCount: {
        type: Number,
        required: [true, 'Generated count is required'],
        min: [0, 'Generated count cannot be negative'],
        default: 0
    },
    userTier: {
        type: String,
        required: [true, 'User tier is required'],
        enum: {
            values: ['FREE', 'PRO'],
            message: 'User tier must be either FREE or PRO'
        },
        default: 'FREE'
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Compound unique index to ensure one record per user per day
userRecipeUsageSchema.index({ userId: 1, date: 1 }, { unique: true });
// Index for efficient date-based queries
userRecipeUsageSchema.index({ date: -1 });
// Virtual for daily limit based on tier
userRecipeUsageSchema.virtual('dailyLimit').get(function () {
    return this.userTier === 'PRO' ? 15 : 5;
});
// Virtual for remaining generations
userRecipeUsageSchema.virtual('remainingGenerations').get(function () {
    return Math.max(0, this.dailyLimit - this.generatedCount);
});
// Virtual for usage percentage
userRecipeUsageSchema.virtual('usagePercentage').get(function () {
    return Math.min(100, (this.generatedCount / this.dailyLimit) * 100);
});
// Virtual for can generate more
userRecipeUsageSchema.virtual('canGenerateMore').get(function () {
    return this.generatedCount < this.dailyLimit;
});
// Static method to get or create usage record for today
userRecipeUsageSchema.statics.getOrCreateTodayUsage = function (userId, userTier) {
    return tslib_1.__awaiter(this, void 0, void 0, function* () {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        let usage = yield this.findOne({ userId, date: today });
        if (!usage) {
            usage = yield this.create({
                userId,
                date: today,
                generatedCount: 0,
                userTier
            });
        }
        else if (usage.userTier !== userTier) {
            // Update tier if it has changed
            usage.userTier = userTier;
            yield usage.save();
        }
        return usage;
    });
};
// Static method to increment usage count
userRecipeUsageSchema.statics.incrementUsage = function (userId, userTier) {
    return tslib_1.__awaiter(this, void 0, void 0, function* () {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const result = yield this.findOneAndUpdate({ userId, date: today }, {
            $inc: { generatedCount: 1 },
            $set: { userTier }
        }, {
            upsert: true,
            new: true,
            setDefaultsOnInsert: true
        });
        return result;
    });
};
exports.default = mongoose_1.default.model('UserRecipeUsage', userRecipeUsageSchema);
