import mongoose, { Document, Schema } from 'mongoose';

export interface IUserRecipeUsage extends Document {
  userId: mongoose.Types.ObjectId;
  date: Date;
  generatedCount: number;
  userTier: 'FREE' | 'PRO';
  createdAt: Date;
  updatedAt: Date;
}

const userRecipeUsageSchema = new mongoose.Schema<IUserRecipeUsage>({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Account',
    required: true,
    index: true
  },
  date: {
    type: Date,
    required: [true, 'Date is required'],
    validate: {
      validator: function(value: Date) {
        // Ensure the date is set to start of day (00:00:00)
        const startOfDay = new Date(value);
        startOfDay.setHours(0, 0, 0, 0);
        return value.getTime() === startOfDay.getTime();
      },
      message: 'Date must be set to start of day (00:00:00)'
    }
  },
  generatedCount: {
    type: Number,
    required: [true, 'Generated count is required'],
    min: [0, 'Generated count cannot be negative'],
    default: 0
  },
  userTier: {
    type: String,
    required: [true, 'User tier is required'],
    enum: {
      values: ['FREE', 'PRO'],
      message: 'User tier must be either FREE or PRO'
    },
    default: 'FREE'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Compound unique index to ensure one record per user per day
userRecipeUsageSchema.index({ userId: 1, date: 1 }, { unique: true });

// Index for efficient date-based queries
userRecipeUsageSchema.index({ date: -1 });

// Virtual for daily limit based on tier
userRecipeUsageSchema.virtual('dailyLimit').get(function() {
  return this.userTier === 'PRO' ? 15 : 5;
});

// Virtual for remaining generations
userRecipeUsageSchema.virtual('remainingGenerations').get(function() {
  return Math.max(0, this.dailyLimit - this.generatedCount);
});

// Virtual for usage percentage
userRecipeUsageSchema.virtual('usagePercentage').get(function() {
  return Math.min(100, (this.generatedCount / this.dailyLimit) * 100);
});

// Virtual for can generate more
userRecipeUsageSchema.virtual('canGenerateMore').get(function() {
  return this.generatedCount < this.dailyLimit;
});

// Static method to get or create usage record for today
userRecipeUsageSchema.statics.getOrCreateTodayUsage = async function(
  userId: mongoose.Types.ObjectId, 
  userTier: 'FREE' | 'PRO'
) {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  let usage = await this.findOne({ userId, date: today });
  
  if (!usage) {
    usage = await this.create({
      userId,
      date: today,
      generatedCount: 0,
      userTier
    });
  } else if (usage.userTier !== userTier) {
    // Update tier if it has changed
    usage.userTier = userTier;
    await usage.save();
  }

  return usage;
};

// Static method to increment usage count
userRecipeUsageSchema.statics.incrementUsage = async function(
  userId: mongoose.Types.ObjectId,
  userTier: 'FREE' | 'PRO'
) {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const result = await this.findOneAndUpdate(
    { userId, date: today },
    { 
      $inc: { generatedCount: 1 },
      $set: { userTier }
    },
    { 
      upsert: true, 
      new: true,
      setDefaultsOnInsert: true
    }
  );

  return result;
};

export default mongoose.model<IUserRecipeUsage>('UserRecipeUsage', userRecipeUsageSchema);
