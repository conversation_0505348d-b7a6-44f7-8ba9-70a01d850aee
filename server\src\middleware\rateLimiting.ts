import rateLimit from 'express-rate-limit';
import { Request, Response } from 'express';

// Import ipKeyGenerator and type it properly to avoid TS errors
const rateLimit_ = require('express-rate-limit');
const ipKeyGenerator = rateLimit_.ipKeyGenerator as (req: Request) => string;
import { AuthenticatedRequest, UserTier } from '../types';

// General API rate limiting
export const generalRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: (req: Request) => {
    // Check if user is authenticated and get their tier
    const authReq = req as AuthenticatedRequest;
    if (authReq.user?.finances?.subscription_plan) {
      const userTier: UserTier = authReq.user.finances.subscription_plan === 'free' ? 'FREE' : 'PRO';
      return userTier === 'PRO' ? 200 : 100;
    }
    return 50; // Default for unauthenticated users
  },
  message: (req: Request) => {
    const authReq = req as AuthenticatedRequest;
    let limit = 50; // Default for unauthenticated users
    if (authReq.user?.finances?.subscription_plan) {
      const userTier: UserTier = authReq.user.finances.subscription_plan === 'free' ? 'FREE' : 'PRO';
      limit = userTier === 'PRO' ? 200 : 100;
    }

    return {
      success: false,
      statusCode: 429,
      message: 'Too many requests from this IP, please try again later',
      details: {
        limit,
        windowMs: 15 * 60 * 1000,
        retryAfter: Math.round(15 * 60) // seconds
      },
      timestamp: new Date().toISOString()
    };
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request): string => {
    const authReq = req as unknown as AuthenticatedRequest;
    return authReq.user?._id ?? ipKeyGenerator(req);
  }
});

// Recipe generation specific rate limiting
export const recipeGenerationRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // 10 recipe generation requests per hour per user
  message: {
    success: false,
    statusCode: 429,
    message: 'Too many recipe generation requests, please try again later',
    details: {
      limit: 10,
      windowMs: 60 * 60 * 1000,
      retryAfter: Math.round(60 * 60) // seconds
    },
    timestamp: new Date().toISOString()
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request): string => {
    const authReq = req as unknown as AuthenticatedRequest;
    const id = authReq.user?._id ?? ipKeyGenerator(req);
    return `recipe_gen_${id}`;
  },
  skip: (req: Request) => {
    // Skip rate limiting for PRO users (they have daily limits instead)
    const authReq = req as AuthenticatedRequest;
    const userTier: UserTier = authReq.user?.finances?.subscription_plan === 'free' ? 'FREE' : 'PRO';
    return userTier === 'PRO';
  }
});

// Fridge operations rate limiting (more lenient)
export const fridgeOperationsRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 150, // 150 fridge operations per 15 minutes
  message: {
    success: false,
    statusCode: 429,
    message: 'Too many fridge operations, please try again later',
    details: {
      limit: 150,
      windowMs: 15 * 60 * 1000,
      retryAfter: Math.round(15 * 60) // seconds
    },
    timestamp: new Date().toISOString()
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request): string => {
    const authReq = req as unknown as AuthenticatedRequest;
    const id = authReq.user?._id ?? ipKeyGenerator(req);
    return `fridge_ops_${id}`;
  }
});

// Authentication rate limiting (for login attempts)
export const authRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 login attempts per 15 minutes per IP
  message: {
    success: false,
    statusCode: 429,
    message: 'Too many authentication attempts, please try again later',
    details: {
      limit: 5,
      windowMs: 15 * 60 * 1000,
      retryAfter: Math.round(15 * 60) // seconds
    },
    timestamp: new Date().toISOString()
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request) => `auth_${ipKeyGenerator(req)}`
});

// Custom rate limit handler for specific endpoints
export const createCustomRateLimit = (options: {
  windowMs: number;
  max: number;
  message: string;
  keyPrefix?: string;
}) => {
  return rateLimit({
    windowMs: options.windowMs,
    max: options.max,
    message: {
      success: false,
      statusCode: 429,
      message: options.message,
      details: {
        limit: options.max,
        windowMs: options.windowMs,
        retryAfter: Math.round(options.windowMs / 1000)
      },
      timestamp: new Date().toISOString()
    },
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req: Request): string => {
      const authReq = req as unknown as AuthenticatedRequest;
      const prefix = options.keyPrefix || 'custom';
      const id = authReq.user?._id ?? ipKeyGenerator(req);
      return `${prefix}_${id}`;
    }
  });
};
