"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { ChevronDown, X, Plus } from "lucide-react"

interface Item {
  id: string
  label: string
}

interface CustomSearchSelectProps {
  items: Item[]
  placeholder?: string
  onSelect: (items: Item[]) => void
  onAddNew?: (label: string) => void
  selectedItems?: Item[]
  defaultSelectedItems?: Item[]
}

export default function CustomSearchSelect({
  items = [],
  placeholder = "Search...",
  onSelect,
  onAddNew,
  selectedItems: controlledSelectedItems,
  defaultSelectedItems = [],
}: CustomSearchSelectProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [filteredItems, setFilteredItems] = useState<Item[]>(items)
  const [highlightedIndex, setHighlightedIndex] = useState<number>(-1)
  const [internalSelectedItems, setInternalSelectedItems] = useState<Item[]>(defaultSelectedItems)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Use either controlled or uncontrolled selected items
  const selectedItems = controlledSelectedItems !== undefined ? controlledSelectedItems : internalSelectedItems

  // Filter out already selected items from dropdown
  useEffect(() => {
    const filtered = items
      .filter((item) => !selectedItems.some((selected) => selected.id === item.id))
      .filter((item) => item.label.toLowerCase().includes(searchTerm.toLowerCase()))
    setFilteredItems(filtered)
    setHighlightedIndex(-1)
  }, [searchTerm, items, selectedItems])

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) {
      if (e.key === "ArrowDown" || e.key === "Enter" || e.key === " ") {
        setIsOpen(true)
        e.preventDefault()
      }
      return
    }

    switch (e.key) {
      case "ArrowDown":
        setHighlightedIndex((prevIndex) => (prevIndex < filteredItems.length - 1 ? prevIndex + 1 : prevIndex))
        e.preventDefault()
        break
      case "ArrowUp":
        setHighlightedIndex((prevIndex) => (prevIndex > 0 ? prevIndex - 1 : 0))
        e.preventDefault()
        break
      case "Enter":
        if (highlightedIndex >= 0 && highlightedIndex < filteredItems.length) {
          handleSelectItem(filteredItems[highlightedIndex])
        } else if (searchTerm && filteredItems.length === 0 && onAddNew) {
          handleAddNew()
        }
        e.preventDefault()
        break
      case "Escape":
        setIsOpen(false)
        e.preventDefault()
        break
    }
  }

  const handleSelectItem = (item: Item) => {
    const newSelectedItems = [...selectedItems, item]

    if (controlledSelectedItems === undefined) {
      setInternalSelectedItems(newSelectedItems)
    }

    onSelect(newSelectedItems)
    setSearchTerm("")
    // Keep dropdown open for multiple selections
    inputRef.current?.focus()
  }

  const handleRemoveItem = (itemToRemove: Item, e: React.MouseEvent) => {
    e.stopPropagation()
    const newSelectedItems = selectedItems.filter((item) => item.id !== itemToRemove.id)

    if (controlledSelectedItems === undefined) {
      setInternalSelectedItems(newSelectedItems)
    }

    onSelect(newSelectedItems)
  }

  const handleAddNew = () => {
    if (onAddNew && searchTerm.trim()) {
      onAddNew(searchTerm.trim())
      setIsOpen(false)
    }
  }

  const clearSearch = () => {
    setSearchTerm("")
    inputRef.current?.focus()
  }

  return (
    <div className="relative w-full" ref={dropdownRef}>
      

      <div
        className={`
          flex items-center w-full border bg-[#ffffff78]
          rounded-full overflow-hidden
          ${isOpen ? "border-[#bbbbbb] ring-1 ring-[#bbbbbb]" : "border-[#bbbbbb]"}
          transition-all duration-150 ease-in-out
        `}
      >
        <input
          ref={inputRef}
          type="text"
          className="flex-grow px-4 py-2.5 outline-none text-[#000000c6]"
          placeholder={placeholder}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          onFocus={() => setIsOpen(true)}
          onKeyDown={handleKeyDown}
          aria-expanded={isOpen}
          aria-autocomplete="list"
          aria-controls="search-dropdown"
          role="combobox"
        />
        {searchTerm && (
          <button
            type="button"
            onClick={clearSearch}
            className="p-2 text-gray-400 hover:text-gray-600"
            aria-label="Clear search"
          >
            <X className="h-4 w-4" />
          </button>
        )}
        <button
          type="button"
          className="p-2 mr-1 text-gray-500"
          onClick={() => setIsOpen(!isOpen)}
          aria-label={isOpen ? "Close dropdown" : "Open dropdown"}
        >
          <ChevronDown className={`h-4 w-4 transition-transform duration-200 ${isOpen ? "rotate-180" : ""}`} />
        </button>
        
      </div>

      

      {isOpen && (
        <div
          id="search-dropdown"
          className="absolute z-10 w-full mt-2 bg-[#ffffff29] border border-gray-300 rounded-[20px] shadow-lg max-h-60 overflow-y-auto"
          role="listbox"
          style={{
            scrollbarWidth: 'none',
            scrollbarColor: 'transparent transparent',
            backdropFilter: 'blur(20px)',
          }}
        >
          {filteredItems.length > 0 ? (
            <ul>
              {filteredItems.map((item, index) => (
                <li
                  key={item.id}
                  onClick={() => handleSelectItem(item)}
                  className={`
                    px-3 py-2 cursor-pointer text-[#00000080] hover:bg-gray-100
                    ${highlightedIndex === index ? "bg-gray-100" : ""}
                  `}
                  role="option"
                  aria-selected={highlightedIndex === index}
                >
                  {item.label}
                </li>
              ))}
            </ul>
          ) : (
            <div className="p-3 text-gray-500">
              {onAddNew ? (
                <button className="flex items-center w-full text-left hover:text-gray-700" onClick={handleAddNew}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add &quot;{searchTerm}&quot;
                </button>
              ) : (
                "No results found"
              )}
            </div>
          )}
        </div>
      )}

      {/* Selected items horizontal list */}
      {selectedItems.length > 0 && (
        <div className="flex flex-wrap gap-2 mt-2 items-center">
          {selectedItems.map((item) => (
            <div key={item.id} className="flex items-center bg-[#ffffff86] text-sm px-3 py-1 rounded-full"
              style={{
                boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
              }}
            >
              <span className="mr-1 text-[#000000a4]">{item.label}</span>
              <button
                type="button"
                onClick={(e) => handleRemoveItem(item, e)}
                className="text-gray-500 hover:text-gray-700 focus:outline-none"
                aria-label={`Remove ${item.label}`}
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
