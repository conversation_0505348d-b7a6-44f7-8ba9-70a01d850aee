import { Router } from 'express';
import {
  AuthenticateTokenOAuth,
  fridgeOperationsRateLimit,
  validateCreateFridgeItem,
  validateUpdateFridgeItem,
  validateFridgeItemsQuery,
  validateMongoId,
  validatePagination
} from '../middleware';
import {
  getFridgeItems,
  createFridgeItem,
  updateFridgeItem,
  deleteFridgeItem,
  toggleFavorite,
  getFavoriteItems,
  getRecommendations,
  getShelfLifeInfo
} from '../controllers/fridge';

const router = Router();

// Apply authentication and rate limiting middleware to all routes
router.use(AuthenticateTokenOAuth);
router.use(fridgeOperationsRateLimit);

// GET /fridge - Get all fridge items with pagination and filtering
router.get('/', validateFridgeItemsQuery, validatePagination, getFridgeItems);

// POST /fridge - Create new fridge item
router.post('/', validateCreateFridgeItem, createFridgeItem);

// PUT /fridge/:id - Update specific fridge item
router.put('/:id', validateMongoId, validateUpdateFridgeItem, updateFridgeItem);

// DELETE /fridge/:id - Delete specific fridge item
router.delete('/:id', validateMongoId, deleteFridgeItem);

// POST /fridge/:id/favorite - Toggle favorite status
router.post('/:id/favorite', validateMongoId, toggleFavorite);

// GET /fridge/favorites - Get only favorite items
router.get('/favorites', getFavoriteItems);

// GET /fridge/recommendations - Get recommended items
router.get('/recommendations', getRecommendations);

// GET /fridge/shelf-life/:foodName - Get shelf life information for a food item
router.get('/shelf-life/:foodName', getShelfLifeInfo);

export default router;
