"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.processPushNotification = exports.queueDailyRecipeGeneration = exports.processRecipeGeneration = exports.getQueueStats = exports.getJobStatus = exports.queuePushNotification = exports.queueManualRecipeGeneration = exports.initializeJobProcessors = void 0;
const tslib_1 = require("tslib");
const queue_1 = require("../queue");
const recipeGenerationProcessor_1 = require("./recipeGenerationProcessor");
Object.defineProperty(exports, "processRecipeGeneration", { enumerable: true, get: function () { return recipeGenerationProcessor_1.processRecipeGeneration; } });
Object.defineProperty(exports, "queueDailyRecipeGeneration", { enumerable: true, get: function () { return recipeGenerationProcessor_1.queueDailyRecipeGeneration; } });
const notificationProcessor_1 = require("./notificationProcessor");
Object.defineProperty(exports, "processPushNotification", { enumerable: true, get: function () { return notificationProcessor_1.processPushNotification; } });
const schemas_1 = require("../../database/schemas");
// Module-scoped flag to ensure processors are only initialized once
let processorsInitialized = false;
/**
 * Initialize all job processors
 */
const initializeJobProcessors = () => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    // Return early if processors have already been initialized
    if (processorsInitialized) {
        console.log('Job processors already initialized, skipping...');
        return;
    }
    console.log('Initializing job processors...');
    // Recipe generation processor
    queue_1.recipeGenerationQueue.process('generate-recipe', 5, recipeGenerationProcessor_1.processRecipeGeneration);
    // Notification processors
    queue_1.notificationQueue.process('send-push-notification', 10, notificationProcessor_1.processPushNotification);
    // Cleanup processor
    queue_1.cleanupQueue.process('database-cleanup', 1, processDatabaseCleanup);
    // Mark processors as initialized to prevent duplicate registration
    processorsInitialized = true;
    console.log('All job processors initialized successfully');
});
exports.initializeJobProcessors = initializeJobProcessors;
/**
 * Database cleanup processor
 */
function processDatabaseCleanup(job) {
    return tslib_1.__awaiter(this, void 0, void 0, function* () {
        try {
            console.log('Starting database cleanup...');
            // Clean up old user recipe usage records (older than 30 days)
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            const deletedUsageRecords = yield schemas_1.UserRecipeUsage.deleteMany({
                date: { $lt: thirtyDaysAgo }
            });
            console.log(`Deleted ${deletedUsageRecords.deletedCount} old usage records`);
            return {
                success: true,
                deletedUsageRecords: deletedUsageRecords.deletedCount,
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            console.error('Database cleanup failed:', error.message);
            throw error;
        }
    });
}
/**
 * Queue a manual recipe generation job
 */
const queueManualRecipeGeneration = (userId, fridgeItemIds, userTier) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const jobPayload = {
            userId,
            expiringItemIds: fridgeItemIds,
            userTier,
            jobType: 'manual'
        };
        const job = yield queue_1.recipeGenerationQueue.add('generate-recipe', jobPayload, {
            attempts: 3,
            backoff: {
                type: 'exponential',
                delay: 2000
            },
            removeOnComplete: 5,
            removeOnFail: 10,
            // Bull priority: 1 is highest priority
            priority: userTier === 'PRO' ? 1 : 5
        });
        const maskedUserId = `${userId.slice(0, 4)}…${userId.slice(-4)}`;
        console.log(`Queued manual recipe generation job ${job.id} for user ${maskedUserId}`);
        return {
            success: true,
            jobId: job.id,
            estimatedDelay: yield getEstimatedProcessingTime()
        };
    }
    catch (error) {
        console.error('Failed to queue manual recipe generation:', error.message);
        throw error;
    }
});
exports.queueManualRecipeGeneration = queueManualRecipeGeneration;
/**
 * Queue a push notification
 */
const queuePushNotification = (userId, title, body, data, delay) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const jobPayload = {
            userId,
            type: 'general',
            title,
            body,
            data
        };
        const job = yield queue_1.notificationQueue.add('send-push-notification', jobPayload, {
            // use nullish coalescing so that an explicit 0 delay is preserved
            delay: delay !== null && delay !== void 0 ? delay : 0,
            attempts: 3,
            backoff: {
                type: 'exponential',
                delay: 1000
            },
            // automatically prune completed and failed jobs
            removeOnComplete: 20,
            removeOnFail: 50
        });
        // mask userId to avoid logging full PII
        const maskedUserId = `${userId.slice(0, 4)}…${userId.slice(-4)}`;
        console.log(`Queued push notification job ${job.id} for user ${maskedUserId}`);
        return {
            success: true,
            jobId: job.id
        };
    }
    catch (error) {
        console.error('Failed to queue push notification:', error.message);
        throw error;
    }
});
exports.queuePushNotification = queuePushNotification;
/**
 * Get estimated processing time for recipe generation
 */
function getEstimatedProcessingTime() {
    return tslib_1.__awaiter(this, void 0, void 0, function* () {
        try {
            const queueStats = yield queue_1.recipeGenerationQueue.getJobCounts();
            const waitingJobs = queueStats.waiting;
            const activeJobs = queueStats.active;
            // Estimate based on queue length (assuming 30 seconds per job)
            const estimatedSeconds = (waitingJobs + activeJobs) * 30;
            return Math.max(estimatedSeconds, 10); // Minimum 10 seconds
        }
        catch (error) {
            return 60; // Default to 1 minute if we can't get stats
        }
    });
}
/**
 * Get job status by ID
 */
const getJobStatus = (jobId, queueType) => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        let queue;
        switch (queueType) {
            case 'recipe':
                queue = queue_1.recipeGenerationQueue;
                break;
            case 'notification':
                queue = queue_1.notificationQueue;
                break;
            case 'cleanup':
                queue = queue_1.cleanupQueue;
                break;
            default:
                throw new Error('Invalid queue type');
        }
        const job = yield queue.getJob(jobId);
        if (!job) {
            return { success: false, error: 'Job not found' };
        }
        const state = yield job.getState();
        const progress = job.progress();
        return {
            success: true,
            jobId: job.id,
            state,
            progress,
            data: job.data,
            createdAt: new Date(job.timestamp),
            processedOn: job.processedOn ? new Date(job.processedOn) : null,
            finishedOn: job.finishedOn ? new Date(job.finishedOn) : null,
            failedReason: job.failedReason
        };
    }
    catch (error) {
        return { success: false, error: error.message };
    }
});
exports.getJobStatus = getJobStatus;
/**
 * Get queue statistics
 */
const getQueueStats = () => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    try {
        const [recipeStats, notificationStats, cleanupStats] = yield Promise.all([
            queue_1.recipeGenerationQueue.getJobCounts(),
            queue_1.notificationQueue.getJobCounts(),
            queue_1.cleanupQueue.getJobCounts()
        ]);
        return {
            success: true,
            stats: {
                recipeGeneration: recipeStats,
                notifications: notificationStats,
                cleanup: cleanupStats
            },
            timestamp: new Date().toISOString()
        };
    }
    catch (error) {
        return { success: false, error: error.message };
    }
});
exports.getQueueStats = getQueueStats;
